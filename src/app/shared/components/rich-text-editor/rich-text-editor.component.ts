import { Component, Input, Output, EventEmitter, forwardRef, inject, signal } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { QuillModule } from 'ngx-quill';
import { UIStore } from '@core/stores';

@Component({
  selector: 'app-rich-text-editor',
  standalone: true,
  imports: [QuillModule],
  template: `
    <div class="rich-text-editor-container">
      <quill-editor
        [ngModel]="value"
        (ngModelChange)="onContentChange($event)"
        [placeholder]="placeholder"
        [readOnly]="disabled"
        [modules]="modules"
        [styles]="editorStyles()"
        class="rich-text-editor"
      ></quill-editor>
    </div>
  `,
  styleUrls: ['./rich-text-editor.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RichTextEditorComponent),
      multi: true
    }
  ]
})
export class RichTextEditorComponent implements ControlValueAccessor {
  @Input() placeholder: string = 'Enter your content...';
  @Input() disabled: boolean = false;
  @Input() minHeight: string = '200px';
  @Output() contentChange = new EventEmitter<string>();

  private uiStore = inject(UIStore);
  
  value: string = '';
  
  // Quill modules configuration
  modules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link']
    ]
  };

  // Dynamic styles based on theme
  editorStyles = signal(() => {
    const isDark = this.uiStore.theme() === 'dark';
    return {
      'min-height': this.minHeight,
      'background-color': isDark ? '#1E1E1E' : '#FFFFFF',
      'color': isDark ? '#FFFFFF' : '#000000',
      'border': `1px solid ${isDark ? '#374151' : '#D1D5DB'}`,
      'border-radius': '8px'
    };
  });

  // ControlValueAccessor implementation
  private onChange = (value: string) => {};
  private onTouched = () => {};

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onContentChange(content: string): void {
    this.value = content;
    this.onChange(content);
    this.onTouched();
    this.contentChange.emit(content);
  }
}
