.rich-text-editor-container {
  @apply w-full;
}

.rich-text-editor {
  @apply w-full;
}

/* Quill editor styling */
:host ::ng-deep .ql-editor {
  @apply text-base-content dark:text-dark-base-content;
  font-family: inherit;
  line-height: 1.6;
  padding: 16px;
}

:host ::ng-deep .ql-toolbar {
  @apply border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100;
  border-bottom: 1px solid;
  border-radius: 8px 8px 0 0;
}

:host ::ng-deep .ql-container {
  @apply border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100;
  border-radius: 0 0 8px 8px;
  font-family: inherit;
}

:host ::ng-deep .ql-toolbar .ql-stroke {
  @apply stroke-neutral-content dark:stroke-dark-neutral-content;
}

:host ::ng-deep .ql-toolbar .ql-fill {
  @apply fill-neutral-content dark:fill-dark-neutral-content;
}

:host ::ng-deep .ql-toolbar .ql-picker-label {
  @apply text-neutral-content dark:text-dark-neutral-content;
}

:host ::ng-deep .ql-toolbar button:hover,
:host ::ng-deep .ql-toolbar button:focus {
  @apply bg-base-200 dark:bg-dark-base-200;
}

:host ::ng-deep .ql-toolbar button.ql-active {
  @apply bg-primary dark:bg-dark-primary;
}

:host ::ng-deep .ql-toolbar button.ql-active .ql-stroke {
  @apply stroke-white;
}

:host ::ng-deep .ql-toolbar button.ql-active .ql-fill {
  @apply fill-white;
}

/* Dropdown styling */
:host ::ng-deep .ql-picker-options {
  @apply bg-base-100 dark:bg-dark-base-100 border-primary-border dark:border-dark-primary-border;
}

:host ::ng-deep .ql-picker-item {
  @apply text-base-content dark:text-dark-base-content;
}

:host ::ng-deep .ql-picker-item:hover {
  @apply bg-base-200 dark:bg-dark-base-200;
}

/* Placeholder styling */
:host ::ng-deep .ql-editor.ql-blank::before {
  @apply text-neutral-content dark:text-dark-neutral-content;
  font-style: normal;
}

/* Focus styling */
:host ::ng-deep .ql-container.ql-snow {
  border: 1px solid;
  @apply border-primary-border dark:border-dark-primary-border;
}

:host ::ng-deep .ql-editor:focus {
  outline: none;
}

:host ::ng-deep .ql-container:focus-within {
  @apply border-primary dark:border-dark-primary;
  box-shadow: 0 0 0 2px rgba(114, 65, 255, 0.1);
}

/* Disabled state */
:host ::ng-deep .ql-toolbar.ql-disabled {
  @apply opacity-50 pointer-events-none;
}

:host ::ng-deep .ql-editor.ql-disabled {
  @apply opacity-75 bg-base-200 dark:bg-dark-base-200;
}
