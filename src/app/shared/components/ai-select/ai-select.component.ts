import {
  Component,
  computed,
  inject,
  OnInit,
  signal,
  ViewEncapsulation,
} from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { APP_ROUTES, TRIGGER_KEYS } from '@core/constants';
import { IAiSelect, ICurrentAi } from '@core/models';
import { AgentIntroService, TriggerService } from '@core/services';
import { AiStore, SocketStore, UserAiStore } from '@core/stores';
import { DxDialog, DxFormField, DxOption, DxPrefix, DxSelect } from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroPlusCircle, heroXMark } from '@ng-icons/heroicons/outline';
import {AiService, SocketService, TourGuideService} from '@shared/services';
import { CommonUtils } from '@shared/utils';
import { switchMap, tap } from 'rxjs';
import { CreateAiComponent } from './create-ai/create-ai.component';

@Component({
  selector: 'app-ai-select',
  templateUrl: './ai-select.component.html',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    DxFormField,
    DxPrefix,
    DxSelect,
    DxOption,
    MatIconModule,
    NgIconsModule,
  ],
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./ai-select.component.css'],
  providers: [provideIcons({ heroPlusCircle, heroXMark })],
})
export class AISelectComponent implements OnInit {
  searchTerm = signal('');

  currentAi = computed(() => this.userAiStore.currentAi()?.id);
  aiOptions = computed((): Array<Partial<ICurrentAi>> => {
    return this.aiStore.ais().map((ai) => ({
      id: ai.id || '',
      name: ai.name || '',
    }));
  });
  filteredOptions = computed((): Array<Partial<ICurrentAi>> => {
    const searchTerm = this.searchTerm();
    const options = this.aiOptions();

    if (!searchTerm) {
      return options;
    }

    return options.filter(
      (option) =>
        option?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option?.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  formGroupCreateAi!: FormGroup;

  userAiStore = inject(UserAiStore);
  aiStore = inject(AiStore);
  private fb = inject(FormBuilder);
  private aiService = inject(AiService);
  private router = inject(Router);
  private triggerService = inject(TriggerService);
  private dialog = inject(DxDialog);
  private socketStore = inject(SocketStore);
  private socketService = inject(SocketService);
  private agentIntroService = inject(AgentIntroService);
  tourGuideService = inject(TourGuideService);
  ngOnInit() {}

  constructor() {}
  updateSearchTerm(term: string) {
    this.searchTerm.set(term);
  }

  clearSearch() {
    this.searchTerm.set('');
  }

  selectAI(aiId: string) {
    if (!aiId || aiId.trim().length === 0) return;
    this.userAiStore.setCurrentAiId(aiId || '');
    this.aiService
      .getListAis()
      .pipe(
        tap((ais: ICurrentAi[]) => {
          this.aiStore.setAis(ais);
          this.aiStore.setAisFilter(ais);
        }),
        switchMap((ais: ICurrentAi[]) => {
          const defaultAiId = ais.find((ai) => ai.default)?.id;
          const selectedAiId =
            this.userAiStore.currentAiId() || defaultAiId || ais[0]?.id || '';
          return this.aiService.selectAi(selectedAiId);
        })
      )
      .subscribe({
        next: (ai: IAiSelect) => {
          this.userAiStore.setCurrentAi(ai?.current_ai);
          const storage = CommonUtils.isRemember()
            ? localStorage
            : sessionStorage;
          storage.setItem('current-ai-id', ai.current_ai.id);
          this.handleAiSelected(ai);

          // Check if agent intro dialog should be shown
          this.agentIntroService
            .shouldShowAgentIntro()
            .subscribe((shouldShow) => {
              if (shouldShow) {
                setTimeout(() => {
                  this.agentIntroService.showAgentIntroDialog();
                }, 2000);
              }
            });

          void this.router.navigate([APP_ROUTES.DASHBOARD]);
        },
      });
    this.clearSearch();
    this.tourGuideService.resumeTour();
  }

  openDialogCreateAi() {
    this.dialog
      .open(CreateAiComponent, {
        data: {},
        width: '30vw',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value && typeof value === 'string' && value.length > 0) {
          this.selectAI(value);

          // Check if agent intro dialog should be shown after creating new AI
          setTimeout(() => {
            this.agentIntroService
              .shouldShowAgentIntro()
              .subscribe((shouldShow) => {
                if (shouldShow) {
                  setTimeout(() => {
                    this.agentIntroService.showAgentIntroDialog();
                  }, 2000);
                }
              });
          }, 1000);
          this.tourGuideService.resumeTour();
          void this.router.navigate([APP_ROUTES.DASHBOARD]);
        }
      });
  }

  private handleAiSelected(ai: IAiSelect) {
    this.socketStore.setSocketCsConnected(null);
    this.socketStore.setSocketMessageConnected(null);
    this.socketStore.setHasCallStartFlow(false);
    this.socketService.setupSocketConnectionCs(ai.current_ai.id, true);
    this.socketService.setupSocketConnectionMessage(ai.current_ai.id, true);
    this.triggerService.trigger(
      TRIGGER_KEYS.RE_INIT_PREVIEW,
      this.triggerService.get(TRIGGER_KEYS.RE_INIT_PREVIEW)() === null
        ? 1
        : this.triggerService.get(TRIGGER_KEYS.RE_INIT_PREVIEW)() + 1
    );
  }
}
