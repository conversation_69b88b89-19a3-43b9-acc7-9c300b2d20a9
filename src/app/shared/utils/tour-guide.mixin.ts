import { inject } from '@angular/core';
import { JoyrideService } from 'ngx-joyride';
import { TourGuideService } from '@shared/services';

export interface TourConfig {
  steps: string[];
  autoStart?: boolean;
  delay?: number;
  priority?: number;
  dependsOn?: string[];
}

export function withTourGuide(config: TourConfig) {
  return class {
    private readonly joyrideService = inject(JoyrideService);
    private readonly tourGuideService = inject(TourGuideService);

    initTour() {
      const { steps, autoStart = true, delay = 500 } = config;

      // Ki<PERSON>m tra các steps chưa hoàn thành
      const incompleteSteps = steps.filter(step => !this.tourGuideService.isStepCompleted(step));
      
      if (autoStart && incompleteSteps.length > 0) {
        setTimeout(() => this.startTour(), delay);
      }
    }

    startTour() {
      this.tourGuideService.configureTour(config);
    }
  };
}