import { Injectable, inject } from '@angular/core';
import { JoyrideService } from 'ngx-joyride';

export interface TourConfig {
  steps: string[];
  autoStart?: boolean;
  delay?: number;
  priority?: number; // S<PERSON> càng nhỏ càng ưu tiên
  dependsOn?: string[]; // Các step phải hoàn thành trước
}

@Injectable({
  providedIn: 'root'
})
export class TourGuideService {
  private readonly COMPLETED_STEPS_KEY = 'dx-connect-completed-steps';
  private readonly SKIP_ALL_KEY = 'dx-connect-skip-all-tours';
  private readonly PAUSED_TOUR_KEY = 'dx-connect-paused-tour';
  private readonly joyrideService = inject(JoyrideService);
  private activeTour: string | null = null;
  private pendingTours: TourConfig[] = [];
  private configuredSteps: Set<string> = new Set();
  private pausedTourState: { currentStep: number; steps: string[] } | null = null;
  private tourSubscription: any = null;
  private currentTourSteps: string[] = [];

  constructor() {}

  configureTour(config: TourConfig): void {
    const { steps, autoStart = true, delay = 500, priority = 100, dependsOn = [] } = config;

    // Kiểm tra xem người dùng đã skip tất cả tours chưa
    if (this.shouldSkipAllTours()) {
      return;
    }

    // Lọc ra các steps chưa hoàn thành
    const incompleteSteps = steps.filter(step => !this.isStepCompleted(step));

    if (incompleteSteps.length === 0) {
      // Tất cả steps đã hoàn thành
      return;
    }

    // Kiểm tra xem các steps này đã được configure chưa
    const stepKey = incompleteSteps.join('-');
    if (this.configuredSteps.has(stepKey)) {
      return;
    }

    // Đánh dấu steps đã được configure
    this.configuredSteps.add(stepKey);

    if (autoStart) {
      // Kiểm tra dependencies (các step phải hoàn thành trước)
      const hasUncompletedDependencies = dependsOn.some(dep => !this.isStepCompleted(dep));

      if (hasUncompletedDependencies) {
        // Nếu có dependency chưa hoàn thành, thêm vào pending
        if (!this.pendingTours.find(t => t.steps.join('-') === stepKey)) {
          this.pendingTours.push({ steps: incompleteSteps, priority, dependsOn });
        }
        return;
      }

      // Thêm tour vào pending list với priority
      if (!this.pendingTours.find(t => t.steps.join('-') === stepKey)) {
        this.pendingTours.push({ steps: incompleteSteps, priority, dependsOn });
      }

      setTimeout(() => {
        this.processPendingTours();
      }, delay);
    }
  }

  private processPendingTours(): void {
    if (this.activeTour || this.pendingTours.length === 0) {
      return;
    }

    // Sắp xếp theo priority (nhỏ hơn = ưu tiên cao hơn)
    this.pendingTours.sort((a, b) => (a.priority || 100) - (b.priority || 100));

    // Lấy tour có priority cao nhất và không có dependency chưa hoàn thành
    const nextTour = this.pendingTours.find(tour => {
      const deps = tour.dependsOn || [];
      return deps.every(dep => this.isStepCompleted(dep));
    });

    if (nextTour) {
      // Xóa khỏi pending list
      const stepKey = nextTour.steps.join('-');
      this.pendingTours = this.pendingTours.filter(t => t.steps.join('-') !== stepKey);
      // Start tour
      this.startTour(nextTour.steps);
    }
  }

  startTour(steps: string[], startFromStep: number = 0): void {
    // Nếu đang có tour khác đang chạy, không start tour mới
    if (this.activeTour) {
      return;
    }

    this.activeTour = steps.join('-'); // Sử dụng step keys làm ID
    this.currentTourSteps = steps; // Lưu lại steps để sử dụng khi pause

    const tourObservable = this.joyrideService.startTour({
      steps: steps,
      startWith: startFromStep > 0 ? steps[startFromStep] : undefined,
      showCounter: false,
      showPrevButton: true,
      logsEnabled: false,
      themeColor: '#7F75CF',
      customTexts: {
        next: 'Next',
        prev: 'Previous',
        done: 'Done',
      }
    });

    // Subscribe và xử lý các sự kiện
    this.tourSubscription = tourObservable.subscribe({
      next: (step: any) => {
        // Đánh dấu step hiện tại đã hoàn thành
        if (step && step.name) {
          this.markStepCompleted(step.name);
        }
        // Mỗi khi chuyển step, gắn lại event listener cho nút close
        setTimeout(() => {
          this.attachCloseButtonListener();
        }, 100);
      },
      complete: () => {
        // Đánh dấu tất cả steps hoàn thành
        const currentStepKey = this.activeTour;
        if (currentStepKey) {
          steps.forEach(step => this.markStepCompleted(step));
          this.activeTour = null;
          this.currentTourSteps = [];
          // Xóa steps khỏi configured list để có thể configure lại nếu cần
          this.configuredSteps.delete(currentStepKey);
          // Xử lý các tour đang chờ
          setTimeout(() => this.processPendingTours(), 100);
        }
      },
      error: (error: any) => {
        // Reset active tour on error
        const currentStepKey = this.activeTour;
        if (currentStepKey) {
          this.activeTour = null;
          this.currentTourSteps = [];
          // Xóa steps khỏi configured list để có thể configure lại nếu cần
          this.configuredSteps.delete(currentStepKey);
          // Xử lý các tour đang chờ
          setTimeout(() => this.processPendingTours(), 100);
        }
      }
    });

    // Gắn listener cho step đầu tiên
    setTimeout(() => {
      this.attachCloseButtonListener();
    }, 100);
  }

  isStepCompleted(stepName: string): boolean {
    const completedSteps = this.getCompletedSteps();
    return completedSteps.includes(stepName);
  }

  markStepCompleted(stepName: string): void {
    const completedSteps = this.getCompletedSteps();
    if (!completedSteps.includes(stepName)) {
      completedSteps.push(stepName);
      localStorage.setItem(this.COMPLETED_STEPS_KEY, JSON.stringify(completedSteps));
    }
  }

  resetStep(stepName: string): void {
    const completedSteps = this.getCompletedSteps();
    const updatedSteps = completedSteps.filter(step => step !== stepName);
    localStorage.setItem(this.COMPLETED_STEPS_KEY, JSON.stringify(updatedSteps));
  }

  resetAllSteps(): void {
    localStorage.removeItem(this.COMPLETED_STEPS_KEY);
    this.configuredSteps.clear();
    this.pendingTours = [];
  }

  private getCompletedSteps(): string[] {
    const stepsJson = localStorage.getItem(this.COMPLETED_STEPS_KEY);
    return stepsJson ? JSON.parse(stepsJson) : [];
  }

  getCompletedStepsList(): string[] {
    return this.getCompletedSteps();
  }

  private shouldSkipAllTours(): boolean {
    return localStorage.getItem(this.SKIP_ALL_KEY) === 'true';
  }

  private setSkipAllTours(): void {
    localStorage.setItem(this.SKIP_ALL_KEY, 'true');
  }

  resetSkipAllTours(): void {
    localStorage.removeItem(this.SKIP_ALL_KEY);
  }

  pauseTour(): void {
    if (!this.activeTour || !this.tourSubscription) {
      return;
    }

    // Lưu trạng thái tour hiện tại
    const currentStepElement = document.querySelector('.joyride-step__header');
    let currentStepIndex = 0;

    if (currentStepElement) {
      const counterText = currentStepElement.textContent;
      const match = counterText?.match(/(\d+)\/(\d+)/);
      if (match) {
        currentStepIndex = parseInt(match[1]) - 1; // Convert to 0-based index
      }
    }

    // Sử dụng steps đã lưu
    this.pausedTourState = {
      currentStep: currentStepIndex,
      steps: this.currentTourSteps
    };

    // Lưu vào localStorage để có thể resume sau khi refresh
    localStorage.setItem(this.PAUSED_TOUR_KEY, JSON.stringify(this.pausedTourState));

    // Dừng tour hiện tại
    if (this.tourSubscription) {
      this.tourSubscription.unsubscribe();
      this.tourSubscription = null;
    }

    // Close joyride
    const closeButton = document.querySelector('.joyride-step__close') as HTMLElement;
    if (closeButton) {
      closeButton.click();
    }

    this.activeTour = null;
  }

  resumeTour(): void {
    // Kiểm tra xem có tour đã pause không
    const pausedStateStr = localStorage.getItem(this.PAUSED_TOUR_KEY);
    if (!pausedStateStr) {
      return;
    }

    const pausedState = JSON.parse(pausedStateStr);
    this.pausedTourState = pausedState;

    // Xóa state đã lưu
    localStorage.removeItem(this.PAUSED_TOUR_KEY);

    // Resume tour từ step đã pause
    if (this.pausedTourState) {
      this.startTour(
        this.pausedTourState.steps,
        this.pausedTourState.currentStep
      );
      this.pausedTourState = null;
    }
  }

  hasPausedTour(): boolean {
    return localStorage.getItem(this.PAUSED_TOUR_KEY) !== null;
  }

  isAnyTourActive(): boolean {
    return this.activeTour !== null;
  }

  getActiveTour(): string | null {
    return this.activeTour;
  }

  private attachCloseButtonListener(): void {
    const closeButton = document.querySelector('.joyride-step__close');
    if (closeButton && !closeButton.hasAttribute('data-skip-listener')) {
      closeButton.setAttribute('data-skip-listener', 'true');
      closeButton.addEventListener('click', () => {
        console.log('Close button clicked - setting skip all tours');
        this.setSkipAllTours();
        // Reset active tour khi user click close
        this.activeTour = null;
        // Clear all pending tours and configured steps
        this.pendingTours = [];
        this.configuredSteps.clear();
      }, { once: true });
    }
  }
}
