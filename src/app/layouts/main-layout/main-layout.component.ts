import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet,
} from '@angular/router';
import {
  AGENT_FLOW_PATH,
  APP_ROUTES,
  MENU_ITEMS,
  STUDIO_PATH,
  TRIGGER_KEYS,
} from '@core/constants';
import { IMenu } from '@core/models';
import { TriggerService } from '@core/services';
import { UIStore, UserAiStore } from '@core/stores';
import { DxButton, DxSidenav, DxSidenavContainer, DxSidenavContent } from '@dx-ui/ui';
import { provideIcons } from '@ng-icons/core';
import {
  heroAtSymbol,
  heroQuestionMarkCircle,
} from '@ng-icons/heroicons/outline';
import { AISelectComponent, SvgIconComponent } from '@shared/components';
import { ClickOutsideDirective, HasRoleDirective } from '@shared/directives';
import { PreviewComponent } from '@views/preview/preview.component';
import { filter } from 'rxjs';
import { MenuItemMultiComponent } from './components/menu-item-multi/menu-item-multi.component';
import { MenuItemComponent } from './components/menu-item/menu-item.component';
import { MobileNavComponent } from './components/mobile-nav/mobile-nav.component';
import { ThemeToggleComponent } from './components/theme-toggle/theme-toggle.component';
import { UserInfoConfigComponent } from './components/user-info-config/user-info-config.component';
import { JoyrideModule } from 'ngx-joyride';
import { TourGuideService } from '@shared/services';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    MenuItemComponent,
    MenuItemMultiComponent,
    PreviewComponent,
    HasRoleDirective,
    AISelectComponent,
    UserInfoConfigComponent,
    ThemeToggleComponent,
    SvgIconComponent,
    DxSidenavContainer,
    DxSidenav,
    DxSidenavContent,
    ClickOutsideDirective,
    CdkOverlayOrigin,
    CdkConnectedOverlay,
    MobileNavComponent,
    JoyrideModule,
    DxButton,
  ],
  providers: [provideIcons({ heroQuestionMarkCircle, heroAtSymbol })],
  templateUrl: './main-layout.component.html',
  styleUrl: './main-layout.component.css',
})
export class MainLayoutComponent implements OnInit {
  menuItems: IMenu[] = [...MENU_ITEMS].filter((v) => v.title !== 'Settings 2');

  leftSideNavMode = signal<'over' | 'side'>('side');
  leftSideNavOpened = signal(true);
  rightSideNavOpened = signal(true);
  showPreview = signal<boolean>(true);
  showStudioStatus = signal<boolean>(false);
  previewKey = signal<number>(0);
  showOptionNeedHelp = signal<boolean>(false);
  mHideNav = signal<boolean>(false);
  hasPausedTour = signal<boolean>(false);

  readonly APP_ROUTES = APP_ROUTES;

  userAiStore = inject(UserAiStore);
  uiStore = inject(UIStore);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private breakpointObserver = inject(BreakpointObserver);
  private triggerService = inject(TriggerService);
  protected tourGuideService = inject(TourGuideService);

  private navigationEndEvent = toSignal(
    this.router.events.pipe(filter((event) => event instanceof NavigationEnd))
  );

  constructor() {
    effect(() => {
      const eventNavigationEnd = this.navigationEndEvent();
      if (eventNavigationEnd) {
        this.handleEventNavigationEnd(eventNavigationEnd.url);
        this.handleRouteData();
      }
    });

    effect(() => {
      const reInitPreview = this.triggerService.get(
        TRIGGER_KEYS.RE_INIT_PREVIEW
      )();
      if (reInitPreview) {
        this.reInitPreview();
      }
    });

    this.breakpointObserver
      .observe([Breakpoints.Handset, Breakpoints.Tablet, '(max-width: 1023px)'])
      .pipe(takeUntilDestroyed())
      .subscribe((result) => {
        this.leftSideNavMode.set(result.matches ? 'over' : 'side');
        if (result.matches) {
          this.leftSideNavOpened.set(false);
          this.rightSideNavOpened.set(false);
        } else {
          this.leftSideNavOpened.set(true);
          this.rightSideNavOpened.set(true);
        }
      });
  }

  ngOnInit(): void {
    // Main layout tour có priority cao nhất
    this.tourGuideService.configureTour({
      steps: ['nav_guided_1', 'main_nav'],
      priority: 1, // Priority cao nhất
      delay: 500
    });

    window.addEventListener('message', (event) => {
      if (event && event?.data && event?.data.type === 'toggle_preview') {
        this.showPreview.set(event?.data.data);
        if (this.showPreview()) {
          this.reInitPreview();
        }
      }
      if (event?.data?.type === 'GO_TO_FLOW_AGENT') {
        const newToolId = event.data.payload;
        window.location.href = `${APP_ROUTES.STUDIO}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.TOOL}/${newToolId}`;
      }
      if (event?.data?.type === 'GO_TO_FLOW_BASIC') {
        const newFlowId = event.data.payload;
        window.location.href = `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BASIC_FLOW}/${newFlowId}`;
      }
    });
  }

  private handleEventNavigationEnd(url: string) {
    const prevShowStudioStatus = this.showStudioStatus();
    const isStudio = url.includes('/studio/');
    const isAdmin = url.includes('/admin/');
    const isPreview = url.includes('/preview');

    this.showStudioStatus.set(isStudio);
    this.showPreview.set(!isAdmin && !isStudio && !isPreview);

    if (prevShowStudioStatus && !isStudio) {
      this.reInitPreview();
    }
  }

  private getDeepestRoute(route: ActivatedRoute): ActivatedRoute {
    while (route.firstChild) {
      route = route.firstChild;
    }
    return route;
  }

  private handleRouteData() {
    const deepestRoute = this.getDeepestRoute(this.route);
    const mHideNav = deepestRoute.snapshot.data?.['mHideNav'];
    this.mHideNav.set(Boolean(mHideNav));
  }

  private reInitPreview() {
    this.previewKey.update((value) => value + 1);
  }

  toggleNeedHelp() {
    this.showOptionNeedHelp.set(!this.showOptionNeedHelp());
  }
}
