@if (!isLoading()) {
<div class="flex flex-col h-full">
  @if (!shouldHideHeader()) {
  <div class="flex items-center justify-between pb-2 text-base-content dark:text-dark-base-content" joyrideStep="builder-step-1" [stepContent]="builderStep1" title="Flow Builder">
    <h1
      class="text-[28px] font-bold"
    >
      Flow Builder
      <a
      href="https://docs.dxconnect.lifesup.ai/Diving%20Deeper/Studio/builder/"
      target="_blank"
      class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
    >
      Learn more
    </a>
    </h1>

    <div class="flex items-center space-x-4" joyrideStep="builder-step-2" [stepContent]="builderStep2" title="Flow Agent">
      <span
        class="text-[15px] text-neutral-content dark:text-dark-neutral-content"
        >Mode:</span
      >
      <div
        class="p-1 rounded-xl cursor-pointer bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
        (click)="toggleMode()"
      >
        <div
          class="flex flex-row overflow-hidden text-neutral-content dark:text-dark-neutral-content bg-transparent"
        >
          <div
            class="flex items-center justify-center space-x-2 rounded-lg px-4 py-1 min-w-[100px]"
            [ngClass]="{
              'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs dark:inset-shadow-dark-decoration-100 shadow-md':
                mode() === 'basic'
            }"
          >
            <app-svg-icon
              type="icBasicFlow"
              class="w-5 h-5"
              [ngClass]="[
                mode() === 'basic'
                  ? '!text-base-content dark:!text-dark-base-content'
                  : '!text-neutral-content dark:!text-dark-neutral-content'
              ]"
            ></app-svg-icon>
            <div
              class="text-sm"
              [ngClass]="[
                mode() === 'basic' ? 'font-semibold' : 'font-medium',
                mode() === 'basic'
                  ? 'text-base-content dark:text-dark-base-content'
                  : 'text-neutral-content dark:text-dark-neutral-content'
              ]"
            >
              Basic
            </div>
          </div>
          <div
            class="flex items-center justify-center space-x-2 rounded-lg px-4 py-1.5 min-w-[100px]"
            [ngClass]="{
              'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs inset-shadow-decoration-100 shadow-md':
                mode() === 'agent'
            }"
          >
            <app-svg-icon
              type="icAgentFlow"
              class="w-5 h-5"
              [ngClass]="[
                mode() === 'agent'
                  ? '!text-base-content dark:!text-dark-base-content'
                  : '!text-neutral-content dark:!text-dark-neutral-content'
              ]"
            ></app-svg-icon>
            <div
              class="text-sm"
              [ngClass]="[
                mode() === 'agent' ? 'font-semibold' : 'font-medium',
                mode() === 'agent'
                  ? 'text-base-content dark:text-dark-base-content'
                  : 'text-neutral-content dark:text-dark-neutral-content'
              ]"
            >
              Agent
            </div>
          </div>
        </div>
      </div>
      <!--<span
        class="text-[15px] text-neutral-content dark:text-dark-neutral-content"
        >Filter:</span
      >
      <dx-form-field
        [style.margin-bottom]="0"
        [style.&#45;&#45;dx-form-field-label-offset-y]="0"
        [subscriptHidden]="true"
      >
        <dx-select
          [formControl]="filterType"
          (valueChange)="onFilterChange($event)"
        >
          <dx-option value="basic/agent">Basic/Agent</dx-option>
          <dx-option value="event">Event</dx-option>
        </dx-select>
      </dx-form-field>-->
      <app-flow-env-select></app-flow-env-select>
    </div>
  </div>
  }

  <div class="flex-1 overflow-auto">
    <router-outlet></router-outlet>
  </div>
</div>
}

<ng-template #builderStep1>
  <div>
    <p class="mb-3">Welcome to the Flow Builder - your visual chatbot creation studio! Here you can:</p>
    <ul class="list-disc ml-5 text-sm mb-3">
      <li>Create new flows from scratch or templates</li>
      <li>Edit existing flows with drag-and-drop interface</li>
      <li>Test flows before publishing</li>
      <li>Publish flows to make them live</li>
      <li>Version control for flow management</li>
    </ul>
    <p class="text-sm font-semibold mb-2">Key Actions:</p>
    <ul class="list-disc ml-5 text-sm">
      <li><strong>Green "Publish" button:</strong> Make your flow live</li>
      <li><strong>Blue "Edit" button:</strong> Modify flow structure</li>
      <li><strong>"Test" option:</strong> Preview conversations before going live</li>
    </ul>
  </div>
</ng-template>

<ng-template #builderStep2>
  <div>
    <p class="mb-3">Choose your development mode based on complexity:</p>
    <div class="mb-3">
      <p class="font-semibold text-sm mb-2">Basic Flow Mode:</p>
      <ul class="list-disc ml-5 text-sm mb-3">
        <li>Visual drag-and-drop builder</li>
        <li>Pre-defined conversation paths</li>
        <li>Best for: FAQs, forms, simple workflows</li>
        <li>Full control over each conversation step</li>
      </ul>
    </div>
    <div>
      <p class="font-semibold text-sm mb-2">Agent Flow Mode:</p>
      <ul class="list-disc ml-5 text-sm">
        <li>AI-powered conversations</li>
        <li>Natural language understanding</li>
        <li>Best for: Complex support, dynamic responses</li>
        <li>Integrates with knowledge base and tools</li>
      </ul>
    </div>
    <p class="text-sm mt-3">Toggle between modes using the switch at the top of the interface.</p>
  </div>
</ng-template>
