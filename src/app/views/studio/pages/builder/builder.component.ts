import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { AGENT_FLOW_PATH, APP_PATH, STUDIO_PATH } from '@core/constants';
import { StudioStore } from '@core/stores';
import {
  DxDialog,
  DxFormField,
  DxSelect,
  DxSnackBar,
} from '@dx-ui/ui';
import {
  ConfirmDialogComponent,
  FlowEnvSelectComponent,
  SvgIconComponent,
} from '@shared/components';
import { SettingsService } from '@shared/services';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import {JoyrideModule} from 'ngx-joyride';
import {TourGuideService} from '@shared/services';

type BuilderMode = 'agent' | 'basic';
type FilterType = 'basic/agent' | 'event';

@Component({
  selector: 'app-builder',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    SvgIconComponent,
    FlowEnvSelectComponent,
    ReactiveFormsModule,
    DxFormField,
    DxSelect,
    JoyrideModule,
  ],
  templateUrl: './builder.component.html',
  styleUrls: ['./builder.component.css'],
})
export class BuilderComponent implements OnInit, OnDestroy {
  private router = inject(Router);
  private settingsService = inject(SettingsService);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);
  private studioStore = inject(StudioStore);
  private readonly tourGuideService = inject(TourGuideService);

  mode = signal<BuilderMode>('basic');
  filterType = new FormControl<FilterType>('basic/agent');
  settingData = signal<any>(null);
  isLoading = signal<boolean>(false);
  currentRoute = signal<string>('');

  shouldHideHeader = computed(() => {
    const route = this.currentRoute();
    return (
      route.includes('/basic-flow/') || route.includes('/agent-flow/tool/')
    );
  });

  private _subcriptions$!: Subscription;

  ngOnInit() {
    this.loadSettings();

    this.currentRoute.set(this.router.url);

    this._subcriptions$ = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.currentRoute.set(event.url);
      });

    // Configure tour guide
    this.tourGuideService.configureTour({
      steps: ['builder-step-1', 'builder-step-2'],
      priority: 10,
      dependsOn: ['main_nav']
    });
  }

  ngOnDestroy(): void {
    this._subcriptions$.unsubscribe();
  }

  private loadSettings() {
    if (this.settingData()) {
      this.initializeModeFromSettings(this.settingData());
      return;
    }
    this.isLoading.set(true);
    this.settingsService.getDetailSetting().subscribe({
      next: (res) => {
        this.settingData.set(res.settings);
        this.initializeModeFromSettings(res.settings);
      },
      error: (err) => {
        this.snackBar.open('Error loading settings', '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
        this.isLoading.set(false);
      },
    });
  }

  private initializeModeFromSettings(settingData: any) {
    const agentEnabled = Boolean(settingData?.basic?.use_agent?.enabled);
    this.mode.set(agentEnabled ? 'agent' : 'basic');

    // Set mode in studio store
    this.studioStore.setMode(agentEnabled ? 'agent' : 'basic');

    const targetRoute = agentEnabled
      ? `${APP_PATH.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.AGENT_TOOL_MAPPING}`
      : `${APP_PATH.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW}`;

    this.router.navigate([targetRoute]).then(() => {
      this.isLoading.set(false);
    });
  }

  toggleMode() {
    const currentMode = this.mode();
    const newMode = currentMode === 'agent' ? 'basic' : 'agent';

    const confirmationData = {
      title: 'Confirm Mode Change',
      htmlContent:
        newMode === 'agent'
          ? 'Switch to Agent Mode? This will enable agent settings and change the system behavior.<br><a href="https://docs.dxconnect.lifesup.ai/Diving%20Deeper/Studio/builder/#overview" target="_blank" class="text-[15px] text-primary underline cursor-pointer hover:opacity-80 hover:italic">Learn more</a>'
          : 'Switch to Basic Mode? This will disable agent settings and revert to basic flow only.<br><a href="https://docs.dxconnect.lifesup.ai/Diving%20Deeper/Studio/builder/#overview" target="_blank" class="text-[15px] text-primary underline cursor-pointer hover:opacity-80 hover:italic">Learn more</a>',
      confirmText: 'Switch Mode',
      cancelText: 'Cancel',
      isDelete: false,
    };

    this.dialog
      .open(ConfirmDialogComponent, {
        data: confirmationData,
        width: '400px',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((result: any) => {
        if (!!result) {
          this.updateAgentSettings(newMode === 'agent');
        }
      });
  }

  onFilterChange(filterType: FilterType) {
    if (filterType === 'event') {
      const targetRoute = `${APP_PATH.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW_EVENT}`;
      this.router.navigate([targetRoute]);
    } else {
      const currentMode = this.mode();
      const targetRoute =
        currentMode === 'agent'
          ? `${APP_PATH.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.AGENT_TOOL_MAPPING}`
          : `${APP_PATH.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW}`;
      this.router.navigate([targetRoute]);
    }
  }

  private updateAgentSettings(enabled: boolean) {
    this.isLoading.set(true);

    const currentSettings = this.settingData();
    const updatedSettings = {
      ...currentSettings,
      basic: {
        ...currentSettings?.basic,
        use_agent: {
          ...currentSettings?.basic?.use_agent,
          enabled: enabled,
        },
      },
    };

    this.settingsService.updateSetting(updatedSettings).subscribe({
      next: (res) => {
        this.settingData.set(res.updated_settings);
        this.mode.set(enabled ? 'agent' : 'basic');

        // Update mode in studio store
        this.studioStore.setMode(enabled ? 'agent' : 'basic');

        const targetRoute = enabled
          ? `${APP_PATH.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.AGENT_TOOL_MAPPING}`
          : `${APP_PATH.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW}`;

        this.router.navigate([targetRoute]).then(() => {
          this.snackBar.open(
            `Successfully switched to ${enabled ? 'Agent' : 'Basic'} Mode`,
            '',
            {
              panelClass: 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            }
          );
          this.isLoading.set(false);
        });
      },
      error: (err) => {
        this.snackBar.open(
          'Error updating settings: ' + (err.error?.detail || err.message),
          '',
          {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          }
        );
        this.isLoading.set(false);
      },
    });
  }
}
