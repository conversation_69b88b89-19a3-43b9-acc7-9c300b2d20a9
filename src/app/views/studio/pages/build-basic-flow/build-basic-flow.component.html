<div class="relative w-full overflow-hidden" joyrideStep="studio_nodes_overview" [stepContent]="studioNodesOverviewContent" title="Studio Nodes" stepPosition="center">
  <div [id]="rootId"></div>

  @if (debugPanelVisible()) {
  <div
    class="absolute bottom-0 left-0 right-0 h-1/2 bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border shadow-2xl z-10 flex flex-col rounded-t-3xl overflow-hidden"
    [@slideUp]="'in'"
  >
    <div
      class="flex justify-between items-center py-4 mx-4 border-b border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
    >
      <div
        class="text-base font-semibold text-neutral-content dark:text-dark-neutral-content m-0"
      >
        Debug Panel
      </div>
      <div class="flex gap-2 items-center">
        <div class="flex justify-between items-center mr-2">
          <span
            class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full"
            >{{ debugData().length }} entries</span
          >
        </div>
        <button
          class="p-1 w-6 h-6 flex items-center justify-center rounded-full bg-transparent border-0 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300 transition-all duration-200 cursor-pointer"
          (click)="clearDebugData()"
          title="Clear all debug entries"
        >
          <app-svg-icon type="icRefresh" class="w-5 h-5"></app-svg-icon>
        </button>
        <button
          class="p-1 w-6 h-6 flex items-center justify-center rounded-full bg-transparent border-0 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300 transition-all duration-200 cursor-pointer"
          (click)="closeDebugPanel()"
        >
          <app-svg-icon type="icClose" class="w-6 h-6"></app-svg-icon>
        </button>
      </div>
    </div>

    <div class="flex-1 p-4 overflow-y-auto text-gray-700 dark:text-gray-300">
      <div class="flex flex-col gap-2">
        @for (item of debugData(); track item.id) {
        <div
          class="border border-primary-border dark:border-dark-primary-border rounded-lg bg-white dark:bg-gray-800 transition-all duration-200 hover:border-purple-500 hover:shadow-md"
          [class.border-purple-500]="item.expanded"
        >
          <div
            class="flex justify-between items-center p-3 cursor-pointer select-none"
            (click)="toggleDebugItem(item.id)"
          >
            <div class="flex flex-col gap-1 flex-1">
              <span
                class="text-xs text-gray-500 dark:text-gray-400 font-mono"
                >{{ item.time }}</span
              >
              <span
                class="text-sm font-medium text-gray-900 dark:text-gray-100"
                >{{ item.title }}</span
              >
            </div>
            <button
              class="p-1 w-6 h-6 flex items-center justify-center rounded bg-transparent border-0 cursor-pointer transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <span
                class="text-xs text-gray-500 dark:text-gray-400 transition-transform duration-200"
                [class.rotate-180]="item.expanded"
                >▼</span
              >
            </button>
          </div>

          @if (item.expanded) {
          <div
            class="px-3 pb-3 border-t border-gray-100 dark:border-gray-700"
            [@slideDown]="'in'"
          >
            <div class="bg-gray-50 dark:bg-gray-900 rounded-md p-3 mt-2">
              <div
                class="flex justify-between items-start py-1.5 border-b border-gray-200 dark:border-gray-700 gap-3 last:border-b-0"
              >
                <span
                  class="text-xs font-semibold text-gray-500 dark:text-gray-400 min-w-[100px] flex-shrink-0"
                  >Conversation ID:</span
                >
                <span
                  class="text-xs text-gray-700 dark:text-gray-300 font-mono break-all text-right flex-1"
                  >{{ formatDebugValue(item.conversation_id) }}</span
                >
              </div>
              <div
                class="flex justify-between items-start py-1.5 border-b border-gray-200 dark:border-gray-700 gap-3 last:border-b-0"
              >
                <span
                  class="text-xs font-semibold text-gray-500 dark:text-gray-400 min-w-[100px] flex-shrink-0"
                  >Node ID:</span
                >
                <span
                  class="text-xs text-gray-700 dark:text-gray-300 font-mono break-all text-right flex-1"
                  >{{ formatDebugValue(item.node_id) }}</span
                >
              </div>
              <div
                class="flex justify-between items-start py-1.5 border-b border-gray-200 dark:border-gray-700 gap-3 last:border-b-0"
              >
                <span
                  class="text-xs font-semibold text-gray-500 dark:text-gray-400 min-w-[100px] flex-shrink-0"
                  >Edge:</span
                >
                <div
                  class="text-xs text-gray-700 dark:text-gray-300 font-mono break-all text-right flex-1"
                >
                  @if (item.parsedEdge) {
                  <div class="flex flex-col gap-2 w-full">
                    <div
                      class="flex flex-col gap-1 p-2 bg-blue-50 dark:bg-slate-900 rounded border-l-2 border-purple-500"
                    >
                      <div class="flex justify-between items-center gap-2">
                        <span
                          class="font-semibold text-slate-600 dark:text-slate-400"
                          >Connection:</span
                        >
                        <span
                          class="font-mono text-slate-800 dark:text-slate-200"
                          >{{ item.parsedEdge.source }} →
                          {{ item.parsedEdge.target }}</span
                        >
                      </div>
                      <div class="flex justify-between items-center gap-2">
                        <span
                          class="font-semibold text-slate-600 dark:text-slate-400"
                          >Type:</span
                        >
                        <span
                          class="font-mono text-slate-800 dark:text-slate-200"
                          >{{ item.parsedEdge.type }}</span
                        >
                      </div>
                    </div>

                    @if (item.parsedEdge.sourceHandle ||
                    item.parsedEdge.targetHandle) {
                    <div
                      class="flex flex-col gap-1 p-1.5 bg-gray-50 dark:bg-slate-800 rounded border-l border-gray-300 dark:border-slate-600"
                    >
                      @if (item.parsedEdge.sourceHandle) {
                      <div
                        class="flex justify-between items-start gap-2 text-xs"
                      >
                        <span
                          class="font-semibold text-slate-600 dark:text-slate-400"
                          >Source Handle:</span
                        >
                        <span
                          class="font-mono text-slate-800 dark:text-slate-200"
                          >{{ item.parsedEdge.sourceHandle }}</span
                        >
                      </div>
                      } @if (item.parsedEdge.targetHandle) {
                      <div
                        class="flex justify-between items-start gap-2 text-xs"
                      >
                        <span
                          class="font-semibold text-slate-600 dark:text-slate-400"
                          >Target Handle:</span
                        >
                        <span
                          class="font-mono text-slate-800 dark:text-slate-200"
                          >{{ item.parsedEdge.targetHandle }}</span
                        >
                      </div>
                      }
                    </div>
                    } @if (item.parsedEdge.animated !== undefined ||
                    item.parsedEdge.style) {
                    <div
                      class="flex flex-col gap-1 p-1.5 bg-gray-50 dark:bg-slate-800 rounded border-l border-gray-300 dark:border-slate-600"
                    >
                      @if (item.parsedEdge.animated !== undefined) {
                      <div
                        class="flex justify-between items-start gap-2 text-xs"
                      >
                        <span
                          class="font-semibold text-slate-600 dark:text-slate-400"
                          >Animated:</span
                        >
                        <span
                          class="font-mono text-slate-800 dark:text-slate-200"
                          >{{ item.parsedEdge.animated ? "Yes" : "No" }}</span
                        >
                      </div>
                      } @if (item.parsedEdge.style) {
                      <div
                        class="flex justify-between items-start gap-2 text-xs"
                      >
                        <span
                          class="font-semibold text-slate-600 dark:text-slate-400"
                          >Style:</span
                        >
                        <span
                          class="font-mono text-slate-800 dark:text-slate-200"
                          >{{ formatStyleObject(item.parsedEdge.style) }}</span
                        >
                      </div>
                      }
                    </div>
                    } @if (item.parsedEdge.data) {
                    <div
                      class="flex flex-col gap-1 p-1.5 bg-gray-50 dark:bg-slate-800 rounded border-l border-gray-300 dark:border-slate-600"
                    >
                      <div
                        class="flex justify-between items-start gap-2 text-xs"
                      >
                        <span
                          class="font-semibold text-slate-600 dark:text-slate-400"
                          >Data:</span
                        >
                        <span
                          class="font-mono text-slate-800 dark:text-slate-200"
                          >{{ item.parsedEdge.data | json }}</span
                        >
                      </div>
                    </div>
                    }
                  </div>
                  } @else {
                  <span
                    class="font-mono text-gray-500 dark:text-gray-400 italic"
                    >{{ formatDebugValue(item.edge) }}</span
                  >
                  }
                </div>
              </div>
              <div
                class="flex justify-between items-start py-1.5 border-b border-gray-200 dark:border-gray-700 gap-3 last:border-b-0"
              >
                <span
                  class="text-xs font-semibold text-gray-500 dark:text-gray-400 min-w-[100px] flex-shrink-0"
                  >Is Prompt:</span
                >
                <span
                  class="text-xs text-gray-700 dark:text-gray-300 font-mono break-all text-right flex-1"
                  >{{ item.is_prompt ? "Yes" : "No" }}</span
                >
              </div>
              <div
                class="flex justify-between items-start py-1.5 border-b border-gray-200 dark:border-gray-700 gap-3 last:border-b-0"
              >
                <span
                  class="text-xs font-semibold text-gray-500 dark:text-gray-400 min-w-[100px] flex-shrink-0"
                  >Output Key:</span
                >
                <span
                  class="text-xs text-gray-700 dark:text-gray-300 font-mono break-all text-right flex-1"
                  >{{ item.output_key }}</span
                >
              </div>
              <div class="flex justify-between items-start py-1.5 gap-3">
                <span
                  class="text-xs font-semibold text-gray-500 dark:text-gray-400 min-w-[100px] flex-shrink-0"
                  >Result:</span
                >
                <span
                  class="text-xs text-gray-700 dark:text-gray-300 font-mono break-all text-right flex-1"
                  >{{ item.result }}</span
                >
              </div>
            </div>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </div>
  }
</div>

<!-- Tour Guide Templates -->
<ng-template #studioNodesOverviewContent>
  <div>
    <p class="mb-3">Studio Nodes are building blocks for creating chatbot flows. The left panel shows node categories:</p>

    <div class="mb-3">
      <p class="font-semibold text-sm mb-2">📝 Prompt Nodes:</p>
      <ul class="list-disc ml-5 text-sm mb-3">
        <li><strong>Question Node:</strong> Ask questions with fallback for invalid inputs</li>
        <li>Validates user responses automatically</li>
        <li>Supports multiple retry attempts</li>
        <li>Custom error messages for different scenarios</li>
      </ul>
    </div>

    <div class="mb-3">
      <p class="font-semibold text-sm mb-2">💬 Message Nodes:</p>
      <ul class="list-disc ml-5 text-sm mb-3">
        <li><strong>Text Node:</strong> Display text messages with language variations</li>
        <li><strong>Carousel Node:</strong> Show images in grid or gallery format</li>
        <li>Rich media support (images, videos, files)</li>
        <li>Multi-language message variants</li>
      </ul>
    </div>

    <div class="mb-3">
      <p class="font-semibold text-sm mb-2">⚡ Action Nodes:</p>
      <ul class="list-disc ml-5 text-sm mb-3">
        <li><strong>Tool Node:</strong> Execute prompts on user input</li>
        <li><strong>Set Variable:</strong> Assign values to variables</li>
        <li><strong>Call API:</strong> Connect to external APIs</li>
        <li><strong>Switch Flow:</strong> Transition between flows</li>
      </ul>
    </div>

    <div class="mb-3">
      <p class="font-semibold text-sm mb-2">🔄 Logic Nodes:</p>
      <ul class="list-disc ml-5 text-sm">
        <li><strong>Condition Node:</strong> Branch flow based on comparisons</li>
        <li>Simple operators (=, >, <, contains)</li>
        <li>Complex AND/OR logic support</li>
        <li>Multiple branch paths</li>
      </ul>
    </div>

    <p class="text-sm mt-3 font-semibold">💡 Usage Tip: Drag nodes from the left panel onto the canvas to add them to your flow!</p>
  </div>
</ng-template>
