import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  APP_ROUTES,
  STUDIO_PATH,
  STUDIO_STATUS,
  TRIGGER_KEYS,
} from '@core/constants';
import { TriggerService } from '@core/services';
import { StudioStore, UIStore } from '@core/stores';
import { DxDialog } from '@dx-ui/ui';
import { IFlowDev } from '@shared/models';
import { DebugMessageService, DebugMessage } from '@shared/services';
import { SvgIconComponent } from '@shared/components/svg-icon/svg-icon.component';
import EditorBasicFlowView from '@views/flow-editor-v1/views/EditorBasicFlowView';
import { PublishFlowDialogComponent } from '@views/studio/components/publish-flow-dialog/publish-flow-dialog.component';
import * as React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { Subject, Subscription } from 'rxjs';
import { v4 as uuid } from 'uuid';
import {JoyrideModule} from 'ngx-joyride';

// Interface for parsed edge data
interface ParsedEdgeData {
  source: string;
  target: string;
  type: string;
  sourceHandle?: string;
  targetHandle?: string;
  id?: string;
  data?: any;
  animated?: boolean;
  style?: any;
}

// Interface for UI debug item
interface DebugItem {
  id: string;
  time: string;
  title: string;
  expanded: boolean;
  // Secondary fields (expandable)
  conversation_id: string;
  node_id: string;
  edge: string | object | null;
  parsedEdge: ParsedEdgeData | null;
  is_prompt: boolean;
  output_key: string;
  result: string;
}

@Component({
  selector: 'app-build-basic-flow',
  standalone: true,
  imports: [CommonModule, SvgIconComponent, JoyrideModule],
  templateUrl: './build-basic-flow.component.html',
  animations: [
    trigger('slideUp', [
      transition(':enter', [
        style({ transform: 'translateY(100%)', opacity: 0 }),
        animate('300ms ease-out', style({ transform: 'translateY(0%)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-in', style({ transform: 'translateY(100%)', opacity: 0 }))
      ])
    ]),
    trigger('slideDown', [
      transition(':enter', [
        style({ opacity: 0, maxHeight: '0px' }),
        animate('200ms ease-out', style({ opacity: 1, maxHeight: '200px' }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ opacity: 0, maxHeight: '0px' }))
      ])
    ])
  ]
})
export class BuildBasicFlowComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  flowId = input.required<number>();

  rootId: string = 'flow-canvas';
  root: Root | undefined;
  protected destroy$ = new Subject<void>();

  // Debug panel state (separate from existing debug mode)
  debugPanelVisible = signal(false);

  // Real-time WebSocket debug data
  debugData = signal<DebugItem[]>([]);

  // Conversation ID for message filtering
  private UUID: string = uuid();

  // Service subscription for debug messages
  private debugMessageSubscription: Subscription = new Subscription();

  private messageListener!: (event: MessageEvent) => void;

  private router: Router = inject(Router);
  private el: ElementRef = inject(ElementRef);
  private dialog = inject(DxDialog);
  private studioStore = inject(StudioStore);
  private uiStore = inject(UIStore);
  private triggerService = inject(TriggerService);
  private debugMessageService = inject(DebugMessageService);

  private previousStatus: string | null = this.studioStore.status();

  private studioStoreEffect = effect(() => {
    const currentStatus = this.studioStore.status();
    if (currentStatus !== this.previousStatus) {
      this.previousStatus = currentStatus;
      void this.router.navigate(['/studio/build']);
    }
  });

  private themeEffect = effect(() => {
    const currentTheme = this.uiStore.theme();
    postMessage({
      type: 'theme',
      data: { theme: currentTheme },
    });
  });

  ngOnInit(): void {
    this.messageListener = (event) => {
      if (event && event?.data && event?.data.type === 'publish_flow') {
        this.publishFlow(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'revert_flow') {
        this.revertFlow(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'debug_mode') {
        this.studioStore.setFlowDebugMode(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'toggle_debug_panel') {
        this.debugPanelVisible.set(event.data.data);
      }
      if (
        event &&
        event?.data &&
        event?.data.type === 'back_to_list_basic_flow'
      ) {
        void this.router.navigate(
          [
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW}`,
          ],
          {
            queryParams: {
              key_word: '',
              trigger_type: event?.data.data.type,
            },
          }
        );
      }
    };
    window.addEventListener('message', this.messageListener);

    // Subscribe to debug messages from the service
    this.setupDebugMessageSubscription();

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'false');
  }

  ngAfterViewInit(): void {
    this.render();
    postMessage({
      type: 'studio_status',
      data: { status: this.studioStore.status() },
    });
    // Send conversation_id to React component for WebSocket communication
    postMessage({
      type: 'conversation_id',
      data: this.UUID
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up debug message subscription
    this.debugMessageSubscription.unsubscribe();

    if (this.root) {
      this.root.unmount();
      this.root = undefined;
    }

    // Reset debug mode when component is destroyed
    this.studioStore.setFlowDebugMode(false);

    // Reset debug panel state
    this.debugPanelVisible.set(false);

    // Remove message listener
    window.removeEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'true');
  }

  private publishFlow(flow: IFlowDev) {
    if (!flow) return;
    if (this.studioStore.status() === STUDIO_STATUS.LIVE) return;
    if (this.studioStore.status() === STUDIO_STATUS.DEV) {
      this.dialog
        .open(PublishFlowDialogComponent, {
          width: '40dvw',
          minWidth: '340px',
          data: {
            flow_id: flow.id,
            trigger_type: flow.trigger_type,
            isMultiple: false,
          },
        })
        .afterClosed()
        .subscribe((result) => {
          void this.router.navigateByUrl(
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.BASIC_FLOW}`
          );
          if (result) {
            setTimeout(() => {
              this.studioStore.setStudioStatus(STUDIO_STATUS.LIVE);
            }, 200);
          }
        });
    }
  }

  private revertFlow(flow: IFlowDev) {
    // Implementation for revert flow functionality
    console.log('Revert flow:', flow);
  }

  closeDebugPanel(): void {
    console.log('Closing debug panel');
    this.debugPanelVisible.set(false);
    // Notify React component that debug panel was closed
    window.postMessage({
      type: 'debug_panel_closed',
      data: false
    }, '*');
  }

  clearDebugData(): void {
    console.log('Clearing debug data');
    this.debugData.set([]);
  }

  toggleDebugItem(itemId: string): void {
    this.debugData.update(items =>
      items.map(item =>
        item.id === itemId
          ? { ...item, expanded: !item.expanded }
          : item
      )
    );
  }

  // Helper method to format null/empty values for display
  formatDebugValue(value: string | object | null | undefined): string {
    if (value === null || value === undefined || value === '') {
      return 'N/A';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return value;
  }

  // Helper method to parse edge data (handles both objects and JSON strings)
  parseEdgeData(edgeData: string | object | null): ParsedEdgeData | null {
    if (!edgeData || edgeData === 'null' || edgeData === 'undefined') {
      return null;
    }

    try {
      let parsed: any;

      // If it's already an object, use it directly
      if (typeof edgeData === 'object') {
        parsed = edgeData;
      } else {
        // If it's a string, try to parse it as JSON
        parsed = JSON.parse(edgeData);
      }

      return {
        source: parsed.source || 'N/A',
        target: parsed.target || 'N/A',
        type: parsed.type || 'N/A',
        sourceHandle: parsed.sourceHandle,
        targetHandle: parsed.targetHandle,
        id: parsed.id,
        data: parsed.data,
        animated: parsed.animated,
        style: parsed.style
      };
    } catch (error) {
      console.warn('Failed to parse edge data:', edgeData, error);
      return null;
    }
  }

  // Helper method to format style object for display
  formatStyleObject(style: any): string {
    if (!style || typeof style !== 'object') {
      return 'N/A';
    }

    const styleEntries = Object.entries(style)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}: ${value}`);

    return styleEntries.length > 0 ? styleEntries.join(', ') : 'N/A';
  }

  // Helper method to generate a meaningful title for debug entries
  generateDebugTitle(name: string | null | undefined, nodeId: string, isPrompt: boolean, startTime: string): string {
    // If name exists and is not empty, use it
    if (name && name.trim() !== '') {
      return name.trim();
    }

    // If name is empty but node_id exists, use node_id with context
    if (nodeId && nodeId.trim() !== '') {
      const contextPrefix = isPrompt ? 'Prompt' : 'Flow Step';
      return `${contextPrefix}: ${nodeId}`;
    }

    // If both name and node_id are empty, create a descriptive fallback
    const contextType = isPrompt ? 'Prompt Entry' : 'Flow Step';
    const timeStamp = this.formatTimeForTitle(startTime);
    return `${contextType} (${timeStamp})`;
  }

  // Helper method to format timestamp for title display
  private formatTimeForTitle(startTime: string): string {
    try {
      const date = new Date(startTime);
      // Format as HH:MM:SS
      return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      // If timestamp parsing fails, use current time
      return new Date().toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }

  // Helper method to normalize null values (convert string "null" to actual null)
  private normalizeNullValue(value: string | null | undefined): string | null {
    if (value === null || value === undefined || value === '' || value === 'null' || value === 'undefined') {
      return null;
    }
    return value;
  }

  private setupDebugMessageSubscription(): void {
    // Subscribe to debug messages from the service
    this.debugMessageSubscription = this.debugMessageService.debugMessages$.subscribe({
      next: (message) => {
        console.log('📨 Received debug message from service:', message);
        this.handleDebugMessage(message);
      },
      error: (error) => {
        console.error('❌ Error receiving debug message from service:', error);
      }
    });
  }

  private handleDebugMessage(message: DebugMessage): void {
    try {
      const startTime = message.start_time || new Date().toISOString();
      const debugItem: DebugItem = {
        id: `${message.node_id}_${Date.now()}`, // Create unique ID
        time: startTime,
        title: this.generateDebugTitle(message.name, message.node_id, Boolean(message.is_prompt), startTime),
        expanded: false,
        // Secondary fields
        conversation_id: message.conversation_id,
        node_id: message.node_id,
        edge: message.edge,
        parsedEdge: this.parseEdgeData(message.edge),
        is_prompt: Boolean(message.is_prompt),
        output_key: this.normalizeNullValue(message.output_key) || '',
        result: this.normalizeNullValue(message.result) || ''
      };
      // Add new debug message to the beginning of the array
      this.debugData.update(items => {
        const newItems = [debugItem, ...items];
        return newItems;
      });
    } catch (error) {
      console.error('❌ Error handling debug message:', error, message);
    }
  }

  private render() {
    if (this.flowId()) {
      const reactRoot = this.el.nativeElement.querySelector(`#${this.rootId}`);
      if (reactRoot) {
        if (this.root) {
          this.root.unmount();
        }

        this.root = createRoot(reactRoot);
        this.root.render(
          React.createElement(EditorBasicFlowView, { flowId: this.flowId() })
        );

        postMessage({
          type: 'theme',
          data: { theme: this.uiStore.theme() },
        });
      }
    }
  }
}
