import {CommonModule, NgOptimizedImage} from '@angular/common';
import {Component, inject, OnInit, signal} from '@angular/core';
import {DxCard, DxCardContent, DxCardHeader, DxDialog, DxSlideToggle,} from '@dx-ui/ui';
import {TYPE_INTEGRATION} from '@core/constants';
import {IIntegration} from '@shared/models/integration.model';
import {IntegrationService, MessagesService, TourGuideService} from '@shared/services';
import {EditIntegrationComponent} from '@views/integrations/components/edit-integration/edit-integration.component';
import {UIStore, UserAiStore} from '@core/stores';
import {debounceTime, Subject} from 'rxjs';
import {MobileHeaderComponent, SvgIconComponent} from '@shared/components';
import {Router} from '@angular/router';
import {APP_ROUTES} from '@core/constants';
import {MHeaderLeftDirective} from '@shared/directives';
import {JoyrideModule} from 'ngx-joyride';

@Component({
  selector: 'app-integrations',
  standalone: true,
  imports: [
    CommonModule,
    NgOptimizedImage,
    DxCard,
    DxCardContent,
    DxCardHeader,
    DxSlideToggle,
    MobileHeaderComponent,
    MHeaderLeftDirective,
    SvgIconComponent,
    JoyrideModule,
  ],
  templateUrl: './integrations.component.html',
  styleUrls: ['./integrations.component.css'],
})
export class IntegrationsComponent implements OnInit {
  listIntegrations = signal<IIntegration[]>([
    {
      type: TYPE_INTEGRATION.EMBED,
      title: 'Personal Website',
      subTitle: 'Embed AI assistant on website with custom chat widget.',
      icon: './assets/icon/icon-embed.svg',
    },
    {
      type: TYPE_INTEGRATION.FRESHCHAT,
      title: 'FreshChat',
      subTitle: 'Automate customer chats in FreshChat with our AI assistant.',
      icon: './assets/icon/icon-freshchat.svg',
    },
    {
      type: TYPE_INTEGRATION.MESSENGER,
      title: 'Messenger',
      subTitle:
        'Deploy your AI assistant to Facebook Messenger for instant replies.',
      icon: './assets/icon/icon-messenger.svg',
    },
    {
      type: TYPE_INTEGRATION.WHATSAPP,
      title: 'WhatsApp',
      subTitle:
        'Power your WhatsApp Business with AI-powered automated customer support.',
      icon: './assets/icon/icon-whatsapp.svg',
    },
    {
      type: TYPE_INTEGRATION.SLACK,
      title: 'Slack',
      subTitle: 'Integrate AI assistant into Slack channels for quick queries.',
      icon: './assets/icon/icon-slack.svg',
    },
    {
      type: TYPE_INTEGRATION.ZALO,
      title: 'Zalo',
      subTitle:
        'Connect your AI assistant to Zalo Official Account for engagement.',
      icon: './assets/icon/icon-zalo.svg',
      isEnabled: false,
    },
    {
      type: TYPE_INTEGRATION.TELEGRAM,
      title: 'Telegram',
      subTitle:
        'Deploy AI assistant as Telegram bot for real-time user conversations.',
      icon: './assets/icon/icon-telegram.svg',
      isComingSoon: true,
    },
    {
      type: TYPE_INTEGRATION.DISCORD,
      title: 'Discord',
      subTitle:
        'Integrate AI assistant into Discord servers for seamless community support.',
      icon: './assets/icon/icon-discord.svg',
      isComingSoon: true,
    },
  ]);
  header = {
    urlImg: '',
    text: '',
  };
  apiKey = '';
  aiId = '';
  title = 'Integrations';
  backFn = () => {};
  searchSubmit = new Subject<boolean>();
  dialog = inject(DxDialog);
  private integrationService = inject(IntegrationService);
  private messagesService = inject(MessagesService);
  private currentUserAiStore = inject(UserAiStore);
  private router = inject(Router);
  protected uiStore = inject(UIStore);
  private readonly tourGuideService = inject(TourGuideService);
  ngOnInit(): void {
    this.getListIntegrations();
    this.getListAPIKey();
    this.aiId = this.currentUserAiStore.currentAi()?.id ?? '';
    this.searchSubmit.pipe(debounceTime(300)).subscribe(() => {
      this.getListIntegrations();
    });
    this.backFn = () => {
      this.router.navigate([APP_ROUTES.MENU]);
    };

    // Configure tour with just the step names
    this.tourGuideService.configureTour({
      steps: ['integration-step-1', 'integration-step-2'],
      priority: 10,
      dependsOn: ['main_nav']
    });
  }

  getListIntegrations() {
    this.integrationService.getListIntegrations().subscribe({
      next: (res: any) => {
        const mapListByType: { [key: string]: IIntegration } = {};
        res.items.forEach((item: any) => {
          mapListByType[item.platform_name] = item;
        });
        this.listIntegrations.update((state) => {
          return state.map((item) => {
            const correspondingItem = mapListByType[item.type!];
            if (correspondingItem) {
              const config = JSON.parse(correspondingItem.config ?? null) ?? {};
              return {
                ...item,
                ...correspondingItem,
                ...config,
                config: config,
              };
            } else {
              return {
                id: item.id,
                ...item,
                config: {},
              };
            }
          });
        });
      },
    });
  }

  getListAPIKey(): void {
    this.messagesService.getListAPIKey().subscribe({
      next: (res) => {
        if (res && res.length > 0) {
          this.apiKey = res[0].api_key;
        }
      },
    });
  }

  enableIntegration(value: boolean, data: IIntegration): void {
    if (data.id){
      this.integrationService.updateIntegration({
        id: data.id,
        ai_id: this.aiId,
        platform_name: data.type,
        config: JSON.stringify({
          ...data.config,
          isEnabled: value,
        }),
      }).subscribe({
        next: (res) => {
          if (res) {
            this.searchSubmit.next(true);
          }
        },
        error: (err) => {
          console.error(err);
        }
      });
    } else {
      this.integrationService.createIntegration({
        ai_id: this.aiId,
        platform_name: data.type,
        config: JSON.stringify({
          ...data.config,
          isEnabled: value,
        }),
      }).subscribe({
        next: (res) => {
          if (res) {
            this.searchSubmit.next(true);
          }
        },
        error: (err) => {
          console.error(err);
        }
      });
    }
  }

  showDiaLogEdit(integration: IIntegration) {
    if (integration.isComingSoon) return;
    // if (!integration.isEnabled) return;
    this.dialog.open(EditIntegrationComponent, {
      data: {
        integration: integration,
        apiKey: this.apiKey,
      },
      minWidth: '60dvw',
    })
      .afterClosed()
      .subscribe((value) => {
        this.searchSubmit.next(!!value);
      });
  }
}
