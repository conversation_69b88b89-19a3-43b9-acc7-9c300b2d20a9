@if (!uiStore.isHandset()) {
  <div class="flex flex-col justify-between" joyrideStep="integration-step-1" [stepContent]="integrationStep1" title="Integrations" stepPosition="center">
      <h1 class="text-[28px] font-bold text-base-content dark:text-dark-base-content">
        Integrations
        <a
        href="https://docs.dxconnect.lifesup.ai/integrations"
        target="_blank"
        class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
      >
        Learn more
      </a>
      </h1>
    <p class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content">
      As soon as your AI Assistant is ready, anyone can start talking to your AI
      Assistant using your public link or web widget.
    </p>

    <div class="mt-6 w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      @for (integration of listIntegrations(); track $index) {
        <div class="col-span-1 w-full">
          <dx-card
            appearance="outlined"
            class="card-integration"
            [ngClass]="{
        'cursor-pointer': !integration.isComingSoon
      }"
            [joyrideStep]="$first ? 'integration-step-2' : ''"
            title="Integration"
            [stepContent]="$first ? integrationStep2 : undefined"
            stepPosition="right"
          >
            <dx-card-header>
              <div class="w-full flex items-start justify-between">
                <img
                  [ngSrc]="integration.icon!"
                  alt="Icon description"
                  width="56"
                  height="56"
                  class="w-14 h-14 object-cover"
                  (click)="$event.stopPropagation()"
                />
                @if (!integration.isComingSoon) {
                  <dx-slide-toggle
                    [checked]="!!integration.isEnabled"
                    (checkedChange)="enableIntegration($event,integration)"
                  ></dx-slide-toggle>
                } @else {
                  <div
                    class="px-2 py-1 text-[13px] text-primary-content bg-neutral-content rounded-full"
                  >
                    Coming Soon
                  </div>
                }
              </div>
            </dx-card-header>
            <dx-card-content  (click)="showDiaLogEdit(integration)">
              <h1
                class="text-xl font-bold text-base-content dark:text-dark-base-content"
              >
                {{ integration.title }}
              </h1>
              <div
                class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content"
              >
                {{ integration.subTitle }}
              </div>
            </dx-card-content>
          </dx-card>
        </div>
      }
    </div>
  </div>
} @else {
  <app-mobile-header [title]="title" [backFn]="backFn">
    <div mHeaderLeft class="flex items-center">
      <a
        href="https://docs.dxconnect.lifesup.ai/category/integrations"
        target="_blank"
        class="flex items-center"
      >
        <app-svg-icon type="icInfo" class="w-4 h-4 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
      </a>
    </div>
  </app-mobile-header>
  <div class="w-full h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden" joyrideStep="integration-step-1" [stepContent]="integrationStep1" title="Integrations">
    <p class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content">
      As soon as your AI Assistant is ready, anyone can start talking to your AI
      Assistant using your public link or web widget.
    </p>
    <div class="mt-6 w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      @for (integration of listIntegrations(); track $index) {
        <div class="col-span-1 w-full">
          <dx-card
            appearance="outlined"
            class="card-integration"
            [ngClass]="{
        'cursor-pointer': !integration.isComingSoon
      }"
            [joyrideStep]="$first ? 'integration-step-2' : ''"
            [title]="$first ? 'Integration' : ''"
            [stepContent]="$first ? integrationStep2 : undefined"
            [stepPosition]="$first ? 'right' : 'bottom'"
          >
            <dx-card-header>
              <div class="w-full flex items-start justify-between">
                <img
                  [ngSrc]="integration.icon!"
                  alt="Icon description"
                  width="56"
                  height="56"
                  class="w-14 h-14 object-cover"
                  (click)="$event.stopPropagation()"
                />
                @if (!integration.isComingSoon) {
                  <dx-slide-toggle
                    [checked]="!!integration.isEnabled"
                    (checkedChange)="enableIntegration($event,integration)"
                  ></dx-slide-toggle>
                } @else {
                  <div
                    class="px-2 py-1 text-[13px] text-primary-content bg-neutral-content rounded-full"
                  >
                    Coming Soon
                  </div>
                }
              </div>
            </dx-card-header>
            <dx-card-content  (click)="showDiaLogEdit(integration)">
              <h1
                class="text-xl font-bold text-base-content dark:text-dark-base-content"
              >
                {{ integration.title }}
              </h1>
              <div
                class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content"
              >
                {{ integration.subTitle }}
              </div>
            </dx-card-content>
          </dx-card>
        </div>
      }
    </div>
  </div>
}


<ng-template #integrationStep1>
  <div>
    <p class="mb-3">Integrations connect your AI Assistant to external platforms and services:</p>
    <ul class="list-disc ml-5 text-sm">
      <li>Messaging Platforms (WhatsApp, Facebook Messenger, Telegram)</li>
      <li>CRM Systems (Salesforce, HubSpot)</li>
      <li>Analytics Tools (Google Analytics, Mixpanel)</li>
      <li>Custom APIs via webhooks</li>
      <li>Payment gateways for transactions</li>
    </ul>
    <p class="text-sm mt-3">Each integration extends your AI's reach and capabilities across different channels.</p>
  </div>
</ng-template>

<ng-template #integrationStep2>
  <div>
    <p class="mb-3">Manage your integrations easily:</p>
    <ol class="list-decimal ml-5 text-sm mb-3">
      <li><strong>Enable/Disable:</strong> Use the toggle switch to activate or deactivate</li>
      <li><strong>Configure:</strong> Click on the integration card to access settings</li>
      <li><strong>Test:</strong> Verify connection before going live</li>
      <li><strong>Monitor:</strong> Check integration health and usage stats</li>
    </ol>
    <p class="text-sm font-semibold mb-2">Configuration Steps:</p>
    <ul class="list-disc ml-5 text-sm">
      <li>Enter API credentials or tokens</li>
      <li>Set webhook URLs if required</li>
      <li>Configure data mapping</li>
      <li>Test the connection</li>
      <li>Save and activate</li>
    </ul>
  </div>
</ng-template>
