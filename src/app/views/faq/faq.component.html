@if (!isHandset()) {
<div class="h-full flex flex-col overflow-hidden" joyrideStep="faq_overview"
     [stepContent]="faqOverviewContent"
     title="FAQ System">
  <h1
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"

  >
    Frequently Asked Questions
    <a
      href="https://docs.dxconnect.lifesup.ai/Frequently%20Asked%20Questions#how-it-works"
      target="_blank"
      class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
    >
      Learn more
    </a>
  </h1>
  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center justify-between gap-4">
      <div class="flex items-center justify-between gap-4 flex-wrap">
        <!-- Search Input -->
        <div class="w-full lg:w-96">
          <dx-form-field
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
            <input
              dxInput
              [(ngModel)]="searchModel.key_word"
              (ngModelChange)="changeFilter()"
              [type]="'text'"
              placeholder="Search by Question/Response"
            />
          </dx-form-field>
        </div>

        <!-- Status Filter -->
        <div class="w-full lg:w-64">
          <dx-form-field
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <dx-select
              [(ngModel)]="selectedStatus"
              (ngModelChange)="onStatusChange()"
            >
              @for (status of statusOptions; track $index) {
              <dx-option [value]="status.value">{{ status.label }}</dx-option>
              }
            </dx-select>
          </dx-form-field>
        </div>
      </div>

      <!-- Add FAQ Button -->
      <div class="flex items-center justify-end">
        <button
          dxButton="filled"
          (click)="openFAQDialog()"
          class="px-4 py-2"
          joyrideStep="faq_add"
          [stepContent]="faqAddContent"
          title="Add FAQ"
        >
          <div class="flex items-center justify-between space-x-1">
            <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
            <span class="text-sm font-medium">Add FAQ</span>
          </div>
        </button>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        class="w-full"
        [rows]="listFAQ"
        [columns]="columns"
        [pageIndex]="pageIndex"
        [limit]="searchModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
        [loading]="isLoading()"
      ></app-data-table>
    </div>
  </div>
</div>
} @else {
<app-mobile-header [title]="title" [backFn]="backFn">
  <div mHeaderLeft class="flex items-center">
    <a
      href="https://docs.dxconnect.lifesup.ai/Frequently%20Asked%20Questions#how-it-works"
      target="_blank"
      class="flex items-center"
    >
      <app-svg-icon type="icInfo" class="w-4 h-4 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
    </a>
  </div>
</app-mobile-header>
<div
  class="w-full h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
>
  <div class="flex items-center justify-between space-x-3">
    <!--Search -->
    <dx-form-field
      class="w-full flex-1"
      [style.margin-bottom]="0"
      [style.--dx-form-field-label-offset-y]="0"
      [subscriptHidden]="true"
    >
      <app-svg-icon
        dxPrefix
        type="icSearch"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        [(ngModel)]="searchModel.key_word"
        (ngModelChange)="searchSubject.next($event)"
        [type]="'text'"
        placeholder="Search by Question/Response"
      />
    </dx-form-field>
    <div class="flex-shrink-0 flex items-center space-x-1">
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        (click)="openFAQDialog()"
      >
        <app-svg-icon
          type="icPlus"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        ></app-svg-icon>
      </div>
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
      >
        <app-svg-icon
          type="icFilter"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="viewFilter.set(true)"
        ></app-svg-icon>
      </div>
    </div>
  </div>
  <div
    class="bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border rounded-[12px]"
  >
    <div class="m-h-table">
      <app-data-table
        class="h-full"
        [rows]="listFAQ"
        [columns]="columns"
        [pageIndex]="pageIndex"
        [limit]="searchModel.pageSize"
        [count]="count"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
      ></app-data-table>
    </div>
  </div>
</div>
<app-mobile-drawer [visible]="viewFilter()">
  <div class="w-full h-full bg-base-100 dark:bg-dark-base-100">
    <app-mobile-header
      [title]="'Filter'"
      [backFn]="closeFn"
      [hideMenu]="true"
    ></app-mobile-header>
    <div class="w-full h-full flex flex-col p-4 pt-18 gap-y-3">
      <dx-form-field [style.margin-bottom]="0" [subscriptHidden]="true">
        <dx-label>Status</dx-label>
        <dx-select
          [(ngModel)]="selectedStatus"
          (ngModelChange)="onStatusChange()"
        >
          @for (status of statusOptions; track $index) {
          <dx-option [value]="status.value">{{ status.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>
    </div>
  </div>
</app-mobile-drawer>
}
<!-- Custom row template -->
<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) {
  <!-- Index column -->
  @case ('index') {
  <div class="flex items-center justify-center">
    <span>{{ getRowIndex(row) + 1 }}</span>
  </div>
  }
  <!-- Question column -->
  @case ('question') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
  >
    <div [dxTooltip]="row[column.columnDef]" dxTooltipPosition="below">
      {{ row[column.columnDef] }}
    </div>
  </div>
  }
  <!-- Response column -->
  @case ('response') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Sources column -->
  @case ('files') {
  <div
    class="flex flex-wrap gap-1"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
  >
    @if (row[column.columnDef]?.length > 0) { @for (sourceId of
    row[column.columnDef]; track sourceId; let i = $index; let last = $last) {
    <div
      class="tag px-4 py-1 rounded-xl text-xs"
      [ngStyle]="{
        'background-color': getRandomColor(sourceId),
        color: getTextColor(getRandomColor(sourceId))
      }"
      [dxTooltip]="knowledgeBaseTooltip(getElementById(sourceId))"
      dxTooltipPosition="below"
    >
      {{ getElementById(sourceId)?.name | titlecase }}
    </div>
    } } @else {
    <span class="text-gray-500 text-xs italic">No sources</span>
    }
    <ng-template #noSources>
      <span class="text-gray-500 text-xs italic">No sources</span>
    </ng-template>
  </div>
  }
  <!-- Status column -->
  @case ('faq_status') { @switch (row[column.columnDef]) { @case ('REVIEW') {
  <p
    class="px-4 bg-info text-info-content rounded-full"
    [dxTooltip]="row['status_note'] || ''"
    dxTooltipPosition="above"
  >
    Review
  </p>
  } @case ('NOT_READY') {
  <p
    class="px-4 bg-warning text-warning-content rounded-full"
    [dxTooltip]="row['status_note'] || ''"
    dxTooltipPosition="above"
  >
    Not Ready
  </p>
  } @default {
  <p
    class="px-4 bg-success text-success-content rounded-full"
    [dxTooltip]="row['status_note'] || ''"
    dxTooltipPosition="above"
  >
    Ready
  </p>
  } } }
  <!-- Status note column -->
  <!-- @case ('status_note') {
  <p
    class="px-4 bg-warning text-warning-content rounded-full"
    [dxTooltip]="row[column.columnDef] || ''"
    dxTooltipPosition="above"
  >
    {{ row[column.columnDef] ? "Has been changed" : "" }}
  </p>
  } -->
  <!-- Default for any other columns -->
  @default {
  <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<!-- Custom action template -->
<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <app-svg-icon
      type="icMoreHorizontal"
      class="w-6 h-6 flex items-center justify-center cursor-pointer hover:opacity-80"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    ></app-svg-icon>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions && !row.isContextMenu"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetY: 10
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetY: 10
        }
      ]"
    >
      <ul
        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
        (clickOutside)="row.isActions = false; row.isContextMenu = false"
      >
        @let faqMenuTemplate = faqMenu;
        <ng-container
          [ngTemplateOutlet]="faqMenuTemplate"
          [ngTemplateOutletContext]="{ row: row }"
        ></ng-container>
      </ul>
    </ng-template>

    <!-- Fixed menu khi click phải -->
    @if (row.isActions && row.isContextMenu) {
    <ul
      class="fixed z-50 w-[245px] flex flex-col justify-between p-1 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
      [style.left.px]="row.contextMenuX"
      [style.top.px]="row.contextMenuY"
      (clickOutside)="row.isActions = false; row.isContextMenu = false"
    >
      @let faqMenuTemplate2 = faqMenu;
      <ng-container
        [ngTemplateOutlet]="faqMenuTemplate2"
        [ngTemplateOutletContext]="{ row: row }"
      ></ng-container>
    </ul>
    }
  </div>
</ng-template>

<ng-template #faqMenu let-row="row">
  <!-- Chuyển trạng thái FAQ -->
  <li
    class="border-b pb-1 mb-1 border-primary-border dark:border-dark-primary-border"
  >
    @if (row.faq_status === 'REVIEW') {
    <button
      (click)="
        $event.stopPropagation();
        handleAction('changeStatus', row, 'READY');
        row.isActions = false;
        row.isContextMenu = false
      "
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      [ngClass]="['!text-success dark:!text-dark-success']"
      title="Change status to ready"
    >
      <app-svg-icon
        type="icCheck"
        dxTooltip="Change status to ready"
        dxTooltipPosition="below"
        class="w-6 h-6 flex items-center justify-center"
      ></app-svg-icon>
      <div class="flex items-center justify-between text-[16px] font-medium">
        Ready
      </div>
    </button>
    <button
      (click)="
        $event.stopPropagation();
        handleAction('changeStatus', row, 'NOT_READY');
        row.isActions = false;
        row.isContextMenu = false
      "
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      [ngClass]="['!text-error dark:!text-dark-error']"
      title="Change status to not ready"
    >
      <app-svg-icon
        type="icClose"
        dxTooltip="Change status to not ready"
        dxTooltipPosition="below"
        class="w-6 h-6 flex items-center justify-center"
      ></app-svg-icon>
      <div class="flex items-center justify-between text-[16px] font-medium">
        Not ready
      </div>
    </button>
    } @else {
    <button
      (click)="
        $event.stopPropagation();
        handleAction('changeStatus', row);
        row.isActions = false;
        row.isContextMenu = false
      "
      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg w-full hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      [ngClass]="[
        row.faq_status === 'NOT_READY'
          ? '!text-success dark:!text-dark-success'
          : '!text-error dark:!text-dark-error'
      ]"
      title="{{
        row.faq_status === 'NOT_READY'
          ? 'Change status to ready'
          : 'Change status to not ready'
      }}"
    >
      <app-svg-icon
        [type]="row.faq_status === 'NOT_READY' ? 'icCheck' : 'icClose'"
        class="w-6 h-6 flex items-center justify-center"
      ></app-svg-icon>
      <div class="flex items-center justify-between text-[16px] font-medium">
        {{ row.faq_status === "NOT_READY" ? "Ready" : "Not Ready" }}
      </div>
    </button>
    }
  </li>
  <!-- Edit -->
  <li
    (click)="
      $event.stopPropagation();
      handleAction('edit', row);
      row.isActions = false;
      row.isContextMenu = false
    "
    class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
  >
    <app-svg-icon type="icEdit" class="w-6 h-6"></app-svg-icon>
    Edit
  </li>
  <!-- Delete -->
  <li
    (click)="
      $event.stopPropagation();
      handleAction('delete', row);
      row.isActions = false;
      row.isContextMenu = false
    "
    class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
  >
    <app-svg-icon type="icTrash" class="w-6 h-6"></app-svg-icon>
    Delete
  </li>
</ng-template>

<!-- Tour Guide Templates -->
<ng-template #faqOverviewContent>
  <div>
    <p class="mb-3">FAQ System stores common Q&A pairs for instant bot responses:</p>
    <ul class="list-disc ml-5 text-sm mb-3">
      <li>Store frequently asked questions and answers</li>
      <li>Enable instant AI responses to common queries</li>
      <li>Improve response time and consistency</li>
      <li>Reduce load on knowledge base searches</li>
      <li>Track FAQ effectiveness with analytics</li>
    </ul>
    <p class="text-sm font-semibold mb-2">Best Practices:</p>
    <ul class="list-disc ml-5 text-sm">
      <li>Regular FAQ audits to ensure accuracy</li>
      <li>Keep content up-to-date with product changes</li>
      <li>Prioritize high-volume questions</li>
      <li>Write clear, concise answers</li>
    </ul>
  </div>
</ng-template>

<ng-template #faqAddContent>
  <div>
    <p class="mb-3">Add a new FAQ entry:</p>
    <ol class="list-decimal ml-5 text-sm mb-3">
      <li>Click "Add FAQ" button</li>
      <li>Enter the question customers commonly ask</li>
      <li>Write a clear, helpful answer</li>
      <li>Add relevant tags for categorization</li>
      <li>Set status (Active/Inactive)</li>
      <li>Save the FAQ entry</li>
    </ol>
    <p class="text-sm font-semibold mb-2">Tips for Good FAQs:</p>
    <ul class="list-disc ml-5 text-sm">
      <li>Use natural language for questions</li>
      <li>Keep answers concise but complete</li>
      <li>Include relevant links when helpful</li>
      <li>Test with actual customer queries</li>
    </ul>
  </div>
</ng-template>
