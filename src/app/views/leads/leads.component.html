@if (!isHandset()) {
<div class="h-full flex flex-col overflow-hidden">
  <h1
    class="text-[28px] font-bold text-base-content dark:text-dark-base-content"
    joyrideStep="leads_overview"
    [stepContent]="leadsOverviewContent"
    title="Leads Management"
  >
    Leads Management
    <a
      href="https://docs.dxconnect.lifesup.ai/Leads"
      target="_blank"
      class="text-[15px] ml-2 font-medium text-primary underline cursor-pointer hover:opacity-80 hover:italic"
    >
      Learn more
    </a>
  </h1>

  <div
    class="mt-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    <div class="flex flex-wrap items-center gap-4 justify-between">
      <div class="flex items-center gap-4 flex-wrap">
        <dx-form-field
          class="w-full lg:w-96"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            [(ngModel)]="searchModel.key_word"
            (ngModelChange)="applyFilters()"
            [type]="'text'"
            placeholder="Search by Name/Email/Phone..."
          />
        </dx-form-field>
        <dx-form-field
          class="w-full lg:w-48"
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
        >
          <dx-select
            [(ngModel)]="selectedStatus"
            (ngModelChange)="onStatusChange()"
          >
            @for (status of statusOptions; track $index) {
            <dx-option [value]="status.value">{{ status.label }}</dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>

      <div class="flex items-center justify-end">
        <button
          dx-button="filled"
          (click)="createNewLead()"
          class="px-4 py-2"
          joyrideStep="leads_add"
          [stepContent]="leadsAddContent"
          title="Add Lead"
        >
          <div class="flex items-center justify-between space-x-1">
            <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
            <span class="text-sm font-medium">Add Lead</span>
          </div>
        </button>
      </div>
    </div>

    <div class="flex-1 flex overflow-hidden">
      <app-data-table
        [rows]="listLeads()"
        [columns]="columns"
        [pageIndex]="pageIndex()"
        [limit]="searchModel.pageSize"
        [count]="count()"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        [loading]="isLoading()"
        class="w-full"
      >
        <!-- ... existing templates ... -->
      </app-data-table>
    </div>
  </div>
</div>
} @else {
<app-mobile-header [title]="title" [backFn]="backFn">
  <div mHeaderLeft class="flex items-center">
    <a
      href="https://docs.dxconnect.lifesup.ai/Leads"
      target="_blank"
      class="flex items-center"
    >
      <app-svg-icon type="icInfo" class="w-4 h-4 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
    </a>
  </div>
</app-mobile-header>
<div
  class="w-full h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
>
  <div class="flex items-center justify-between space-x-3">
    <dx-form-field
      class="w-full lg:w-96"
      [style.margin-bottom]="0"
      [style.--dx-form-field-label-offset-y]="0"
      [subscriptHidden]="true"
    >
      <app-svg-icon
        dxPrefix
        type="icSearch"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        [(ngModel)]="searchModel.key_word"
        (ngModelChange)="applyFilters()"
        [type]="'text'"
        placeholder="Search by Name/Email/Phone..."
      />
    </dx-form-field>
    <div class="flex-shrink-0 flex items-center space-x-1">
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        (click)="createNewLead()"
      >
        <app-svg-icon
          type="icPlus"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        ></app-svg-icon>
      </div>
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
      >
        <app-svg-icon
          type="icFilter"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="viewFilter.set(true)"
        ></app-svg-icon>
      </div>
    </div>
  </div>
  <div
    class="bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border rounded-[12px]"
  >
    <div class="m-h-table">
      <app-data-table
        [rows]="listLeads()"
        [columns]="columns"
        [pageIndex]="pageIndex()"
        [limit]="searchModel.pageSize"
        [count]="count()"
        (pageChange)="changePage($event)"
        (action)="onAction($event)"
        [rowTemplate]="rowTemplate"
        [actionTemplate]="actionTemplate"
        class="h-full"
        [loading]="isLoading()"
      >
      </app-data-table>
    </div>
  </div>
</div>
<app-mobile-drawer [visible]="viewFilter()">
  <div class="w-full h-full bg-base-100 dark:bg-dark-base-100">
    <app-mobile-header
      [title]="'Filter'"
      [backFn]="closeFn"
      [hideMenu]="true"
    ></app-mobile-header>
    <div class="w-full h-full flex flex-col p-4 pt-18 gap-y-3">
      <!--Filter -->
      <dx-form-field
        class="w-full lg:w-48"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Status</dx-label>
        <dx-select
          [(ngModel)]="selectedStatus"
          (ngModelChange)="onStatusChange()"
        >
          @for (status of statusOptions; track $index) {
          <dx-option [value]="status.value">{{ status.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>
    </div>
  </div>
</app-mobile-drawer>
}

<!-- Custom row template -->
<ng-template #rowTemplate let-row="row" let-column="column">
  @switch (column.columnDef) {
  <!-- Index column -->
  @case ('index') {
  <div class="flex items-center justify-center">
    <span>{{ getRowIndex(row) + 1 }}</span>
  </div>
  }
  <!-- User name column -->
  @case ('user_name') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{ 'max-width': column.maxWidth, 'min-width': column.minWidth }"
  >
    <div [dxTooltip]="row[column.columnDef]" dxTooltipPosition="below">
      {{ row[column.columnDef] }}
    </div>
  </div>
  }
  <!-- Phone number column -->
  @case ('phone_number') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Email column -->
  @case ('email') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Status column -->
  @case ('status') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
  >
    <span
      class="px-2 rounded-full"
      [ngClass]="getStatusClass(row[column.columnDef])"
    >
      {{ getStatusLabel(row[column.columnDef]) }}
    </span>
  </div>
  }
  <!-- User ID column -->
  @case ('user_id') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  }
  <!-- Update Time column -->
  @case ('update_time') {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="formatDateTime(row[column.columnDef])"
    dxTooltipPosition="below"
  >
    {{ formatDateTime(row[column.columnDef]) }}
  </div>
  }
  <!-- Default for any other columns -->
  @default {
  <div
    class="flex items-center truncate"
    [ngStyle]="{
      'max-width': column.maxWidth,
      'min-width': column.minWidth,
      'justify-content': column.align
    }"
    [dxTooltip]="row[column.columnDef]"
    dxTooltipPosition="below"
  >
    {{ row[column.columnDef] }}
  </div>
  } }
</ng-template>

<!-- Custom action template -->
<ng-template #actionTemplate let-row>
  <div class="flex justify-center items-center">
    <app-svg-icon
      type="icMoreHorizontal"
      class="w-6 h-6 flex items-center justify-center cursor-pointer"
      (click)="row.isActions = !row.isActions"
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
    ></app-svg-icon>
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="row.isActions && !row.isContextMenu"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPositions]="[
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'top',
          offsetY: 10
        },
        {
          originX: 'start',
          originY: 'center',
          overlayX: 'end',
          overlayY: 'bottom',
          offsetY: 10
        }
      ]"
    >
      <ul
        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
        (clickOutside)="row.isActions = false; row.isContextMenu = false"
      >
        <ng-container
          *ngTemplateOutlet="leadMenu; context: { row: row }"
        ></ng-container>
      </ul>
    </ng-template>

    <!-- Context menu khi click phải -->
    <ul
      *ngIf="row.isActions && row.isContextMenu"
      class="w-[245px] p-2 rounded-xl !text-base-content dark:!text-dark-base-content shadow-md flex flex-col gap-y-1 border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 z-50"
      [style.left.px]="row.contextMenuX"
      [style.top.px]="row.contextMenuY"
      (clickOutside)="row.isActions = false; row.isContextMenu = false"
    >
      <ng-container
        *ngTemplateOutlet="leadMenu; context: { row: row }"
      ></ng-container>
    </ul>
  </div>
</ng-template>

<!--List Menu Actions-->
<ng-template #leadMenu let-row="row">
  <!-- Xem chi tiết -->
  <li
    (click)="
      $event.stopPropagation();
      handleAction('view', row);
      row.isActions = false;
      row.isContextMenu = false
    "
    class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
  >
    <app-svg-icon type="icShow" class="w-6 h-6"></app-svg-icon>
    View details
  </li>
  <!-- Sửa -->
  <li
    (click)="
      $event.stopPropagation();
      handleAction('edit', row);
      row.isActions = false;
      row.isContextMenu = false
    "
    class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
  >
    <app-svg-icon type="icEdit" class="w-6 h-6"></app-svg-icon>
    Edit
  </li>
  <!-- Xoá -->
  <li
    (click)="
      $event.stopPropagation();
      handleAction('delete', row);
      row.isActions = false;
      row.isContextMenu = false
    "
    class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
  >
    <app-svg-icon type="icTrash" class="w-6 h-6"></app-svg-icon>
    Delete
  </li>
</ng-template>

<!-- Tour Guide Templates -->
<ng-template #leadsOverviewContent>
  <div>
    <p class="mb-3">Leads Management captures and tracks potential customers from AI conversations:</p>
    <ul class="list-disc ml-5 text-sm mb-3">
      <li>Automatic data capture during chatbot interactions</li>
      <li>Store name, email, phone, and custom fields</li>
      <li>Lead qualification and scoring support</li>
      <li>Export capabilities for sales teams</li>
      <li>Integration with CRM systems</li>
    </ul>
    <p class="text-sm font-semibold mb-2">Key Features:</p>
    <ul class="list-disc ml-5 text-sm">
      <li>Real-time lead capture from conversations</li>
      <li>Custom field mapping for business needs</li>
      <li>Lead status tracking (New, Qualified, Converted)</li>
      <li>Export to CSV for sales follow-up</li>
      <li>Centralized customer engagement tracking</li>
    </ul>
  </div>
</ng-template>

<ng-template #leadsAddContent>
  <div>
    <p class="mb-3">Manually add leads to your database:</p>
    <ol class="list-decimal ml-5 text-sm mb-3">
      <li>Click "Add Lead" button</li>
      <li>Fill in customer information:
        <ul class="list-disc ml-3 mt-1">
          <li>Full name (required)</li>
          <li>Email address</li>
          <li>Phone number</li>
          <li>Company/Organization</li>
          <li>Custom fields as needed</li>
        </ul>
      </li>
      <li>Select lead status (New, Qualified, etc.)</li>
      <li>Add notes or tags for categorization</li>
      <li>Save the lead information</li>
    </ol>
    <p class="text-sm font-semibold mb-2">Best Practices:</p>
    <ul class="list-disc ml-5 text-sm">
      <li>Verify contact information accuracy</li>
      <li>Add context from initial interaction</li>
      <li>Set appropriate follow-up reminders</li>
      <li>Tag leads by source for tracking</li>
    </ul>
  </div>
</ng-template>
