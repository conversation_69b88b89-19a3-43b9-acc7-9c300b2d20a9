@if (uiStore.isHandset()) {
<app-mobile-header [title]="'Dashboard'" [hideBack]="true"></app-mobile-header>
}
<div class="lg:flex justify-between items-center" joyrideStep="dashboard-step-1" [stepContent]="dashboardStep1" title="Dashboard">
  @if (!uiStore.isHandset()) {
  <div>
    <h1
      class="bg-base-200 dark:bg-dark-base-200 px-4 py-3 xl:bg-transparent xl:px-0 xl:py-0 text-[28px] font-bold text-base-content dark:text-dark-base-content">
      Dashboard
    </h1>
    <p class="mt-2 text-[15px] text-neutral-content dark:text-dark-neutral-content hidden xl:block">
      Track and analyze your conversations and performance metrics
    </p>
  </div>
  }

  <div class="w-full lg:w-84 flex gap-4 lg:gap-6 px-4 pb-0 pt-18 lg:p-0 justify-between lg:justify-end">
    <div
      class="modern-date-field w-full text-light-text dark:text-dark-text w-40 bg-light-secondary-background dark:bg-dark-secondary-background rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <mat-form-field appearance="outline" class="w-full custom-mat-form-field">
        <input matInput [matDatepicker]="pickerFrom" [value]="timeFrom" (dateChange)="onDateFromMatChange($event)"
          class="py-2 px-3" />
        <mat-datepicker-toggle matIconSuffix [for]="pickerFrom"></mat-datepicker-toggle>
        <mat-datepicker #pickerFrom></mat-datepicker>
      </mat-form-field>
    </div>

    <div
      class="modern-date-field w-full text-light-text dark:text-dark-text w-40 bg-light-secondary-background dark:bg-dark-secondary-background rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <mat-form-field appearance="outline" class="w-full custom-mat-form-field">
        <input matInput [matDatepicker]="pickerTo" [value]="timeTo" (dateChange)="onDateToMatChange($event)"
          class="py-2 px-3" />
        <mat-datepicker-toggle matIconSuffix [for]="pickerTo"></mat-datepicker-toggle>
        <mat-datepicker #pickerTo></mat-datepicker>
      </mat-form-field>
    </div>
  </div>
</div>

<div class="w-full flex flex-col items-stretch lg:mt-6 space-y-8 p-4 xl:p-0 dashboard-scrollable-content">
  <section>
    <h2 class="text-neutral-content dark:text-dark-neutral-content mb-2">
      Conversations
    </h2>
    <div class="content space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
        <!-- Total Conversations Card -->
        <dx-card appearance="outlined" class="card-dashboard" joyrideStep="dashboard-step-2" [stepContent]="dashboardStep2" title="Widget">
          <dx-card-header>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2 text-light-primary dark:text-dark-primary" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path>
                <path
                  d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z">
                </path>
              </svg>
              <span class="text-base-content dark:text-dark-base-content">Total Conversations</span>
            </div>
          </dx-card-header>
          <dx-card-content>
            @if (dataDashboard.conversation_stats; as stats) {
            <div class="flex justify-between items-center">
              <div>
                <div class="metric-value text-light-text dark:text-dark-text">
                  {{ stats.conversations ?? "N/A" }}
                </div>
                <div class="status-indicator">
                  @if (stats.percentage_conversation_previous_period !==
                  undefined && stats.percentage_conversation_previous_period >
                  0) {
                  <svg class="w-4 h-4" fill="green" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z"
                      clip-rule="evenodd"></path>
                  </svg>
                  } @if (stats.percentage_conversation_previous_period !==
                  undefined && stats.percentage_conversation_previous_period < 0) { <svg class="w-4 h-4" fill="red"
                    viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z"
                      clip-rule="evenodd"></path>
                    </svg>
                    } @if (stats.percentage_conversation_previous_period === 0 ||
                    stats.percentage_conversation_previous_period === undefined) {
                    <svg class="w-4 h-4" fill="orange" viewBox="0 0 20 20">
                      <path fill-rule="evenodd"
                        d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                        clip-rule="evenodd"></path>
                      <path fill-rule="evenodd"
                        d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                        clip-rule="evenodd"></path>
                    </svg>
                    }
                    <span class="text-[15px]" [ngClass]="{
                      positive: getConversationPercentage() !== undefined && getConversationPercentage() > 0,
                      negative: getConversationPercentage() !== undefined && getConversationPercentage() < 0,
                      neutral: getConversationPercentage() === 0 || getConversationPercentage() === undefined
                    }">{{ getConversationPercentage() }}%</span>
                    <span class="text-[15px] text-light-text dark:text-dark-text"> vs previous</span>
                </div>
              </div>
              <div class="animate-fade-in">
                <ng-container *ngTemplateOutlet="
                    templateChartTrendRef;
                    context: { valuePercent: percentConversation }
                  "></ng-container>
              </div>
            </div>
            }
          </dx-card-content>
        </dx-card>

        <!-- Conversation Time Statistics Card -->
        <dx-card appearance="outlined" class="card-dashboard">
          <dx-card-header>
            <div class="flex items-center">
              <span class="text-base-content dark:text-dark-base-content">Conversation Time Statistics</span>
            </div>
          </dx-card-header>
          <dx-card-content>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-[15px] text-neutral-content dark:text-dark-neutral-content">Average time:</span>
                <span class="font-semibold text-light-text dark:text-dark-text">{{
                  dataDashboard.statistics_conversation_time?.average_time
                  | hoursToHMS
                  }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-[15px] text-neutral-content dark:text-dark-neutral-content">Maximum time:</span>
                <span class="font-semibold text-light-text dark:text-dark-text">{{
                  dataDashboard.statistics_conversation_time?.max_time
                  | hoursToHMS
                  }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-[15px] text-neutral-content dark:text-dark-neutral-content">Minimum time:</span>
                <span class="font-semibold text-light-text dark:text-dark-text">{{
                  dataDashboard.statistics_conversation_time?.min_time
                  | hoursToHMS
                  }}</span>
              </div>
            </div>
          </dx-card-content>
        </dx-card>

        <!-- Transferred to Human Agent Card -->
        <dx-card appearance="outlined" class="card-dashboard">
          <dx-card-header>
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2 text-light-primary dark:text-dark-primary" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z">
                </path>
              </svg>
              <span class="text-base-content dark:text-dark-base-content">Transferred to Human Agent</span>
            </div>
          </dx-card-header>
          <dx-card-content>
            <div class="flex justify-between items-center">
              <div #chartTransferredOptionsRef class="chart-container animate-fade-in"></div>
              <div class="status-indicator">
                @if (handoffPercentage !== undefined && handoffPercentage > 0) {
                <svg class="w-4 h-4" fill="green" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z"
                    clip-rule="evenodd"></path>
                </svg>
                } @if (handoffPercentage !== undefined && handoffPercentage < 0) { <svg class="w-4 h-4" fill="red"
                  viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z"
                    clip-rule="evenodd"></path>
                  </svg>
                  } @if (handoffPercentage === 0 || handoffPercentage ===
                  undefined) {
                  <svg class="w-4 h-4" fill="orange" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"></path>
                    <path fill-rule="evenodd"
                      d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z"
                      clip-rule="evenodd"></path>
                  </svg>
                  }

                  <span class="text-[15px]" [ngClass]="{
                    positive: handoffPercentage !== undefined && handoffPercentage > 0,
                    negative: handoffPercentage !== undefined && handoffPercentage < 0,
                    neutral: handoffPercentage === 0 || handoffPercentage === undefined
                  }">{{ handoffPercentage ?? 0 }}%</span>
                  <span class="text-[15px] text-neutral-content dark:text-dark-neutral-content"> vs previous</span>
              </div>
            </div>
          </dx-card-content>
        </dx-card>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Channel Message Statistics Card -->
        <dx-card appearance="outlined" class="card-dashboard">
          <dx-card-header>
            <div class="flex items-center">
              <span class="text-base-content dark:text-dark-base-content">Channel Message Statistics</span>
            </div>
          </dx-card-header>
          <dx-card-content>
            <div class="chart-container animate-fade-in">
              <div #chartChannelMessageStatistics class="w-full h-full"></div>
            </div>
          </dx-card-content>
        </dx-card>

        <!-- Domain Statistics Card -->
        <dx-card appearance="outlined" class="card-dashboard">
          <dx-card-header>
            <div class="flex items-center">
              <span class="text-base-content dark:text-dark-base-content">Domain Statistics for Web Widget</span>
            </div>
          </dx-card-header>
          <dx-card-content>
            <div class="chart-container animate-fade-in">
              <div #chartDomainStatisticsForWebWidgetChannel class="w-full h-full"></div>
            </div>
          </dx-card-content>
        </dx-card>
      </div>

      <!-- Total Conversations Chart Card -->
      <dx-card appearance="outlined" class="card-dashboard">
        <dx-card-header>
          <div class="flex items-center">
            <span class="text-base-content dark:text-dark-base-content">Total Conversations Over Time</span>
          </div>
        </dx-card-header>
        <dx-card-content>
          <div class="chart-container animate-fade-in" style="min-height: 250px">
            <div #chartTotalConversationsRef class="w-full h-full"></div>
          </div>
        </dx-card-content>
      </dx-card>
    </div>
  </section>
  <section>
    <h2 class="text-neutral-content dark:text-dark-neutral-content mb-2">
      Messages
    </h2>
    <div class="content space-y-6" >
      <!-- Message Statistics Cards -->
      <div class="grid grid-cols-1 gap-6">
        <!-- Combined Messages and Response Time Card -->
        <dx-card appearance="outlined" class="card-dashboard"
                 joyrideStep="dashboard-step-3"
                 [stepContent]="dashboardStep3"
                 stepPosition="bottom"
                 title="Usage">
          <dx-card-content>
            <!-- Messages Used and Response Time on the same line -->
            <div class="flex justify-between items-center mb-6">
              <div class="w-full">
                <div class="text-[15px] text-neutral-content dark:text-dark-neutral-content mb-1">
                  Messages Used between ({{ timeFrom | date : "dd/MM/yyyy" }} -
                  {{ timeTo | date : "dd/MM/yyyy" }})
                </div>
                <div class="font-semibold text-light-text dark:text-dark-text">
                  {{ dataDashboard.conversation_stats?.message_numb ?? "N/A" }}
                </div>
              </div>
              <div class="w-full">
                <div class="text-[15px] text-neutral-content dark:text-dark-neutral-content mb-1">
                  Average response time
                </div>
                <div class="font-semibold text-light-text dark:text-dark-text">
                  {{
                  dataDashboard.conversation_stats
                  ?.avg_time_message_response ?? "N/A"
                  }}
                  <span class="text-sm">seconds</span>
                </div>
              </div>
            </div>

            <!-- Messages used in current period with progress bar -->
            <div class="mt-6">
              <div class="text-[15px] text-neutral-content dark:text-dark-neutral-content mb-2">
                Messages used in current period
              </div>
              <div class="flex justify-between items-center mb-2">
                <span class="text-[15px] text-neutral-content dark:text-dark-neutral-content">Usage</span>
                <span class="font-medium text-light-text dark:text-dark-text">{{
                  dataDashboard.percentMessagesPeriod
                  ? dataDashboard.percentMessagesPeriod + "%"
                  : "N/A"
                  }}</span>
              </div>
              <div class="progress-container h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div class="progress-bar h-full bg-light-primary dark:bg-dark-primary rounded-full" [ngStyle]="{
                    width: dataDashboard.percentMessagesPeriod
                      ? dataDashboard.percentMessagesPeriod + '%'
                      : '0%'
                  }"></div>
              </div>
              <div class="flex justify-between text-xs text-neutral-content dark:text-dark-neutral-content mt-1">
                <span>{{ dataDashboard.messages_this_period ?? "0" }} used</span>
                <span>{{ dataDashboard.messages_limit ?? "N/A" }} limit</span>
              </div>
              @if (dataDashboard.percentMessagesPeriod &&
              dataDashboard.percentMessagesPeriod >= 80) {
              <div class="mt-4 text-center">
                <a routerLink="/subscriptions"
                  class="inline-flex items-center px-4 py-2 bg-light-primary dark:bg-dark-primary text-white rounded-md hover:bg-opacity-90 transition-colors duration-200">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                      clip-rule="evenodd"></path>
                  </svg>
                  Upgrade Plan
                </a>
              </div>
              }
            </div>
          </dx-card-content>
        </dx-card>
      </div>

      <!-- Message Cost Card -->
      @if (dataDashboard.statistics_average_cost_message) {
      <dx-card appearance="outlined" class="card-dashboard">
        <dx-card-header>
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2 text-light-primary dark:text-dark-primary" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z">
              </path>
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                clip-rule="evenodd"></path>
            </svg>
            <span class="text-base-content dark:text-dark-base-content">Message Cost Analysis</span>
          </div>
        </dx-card-header>
        <dx-card-content>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div class="text-neutral-content dark:text-dark-neutral-content mb-2">
                Current Period Cost ({{ timeFrom | date : "dd/MM/yyyy" }} -
                {{ timeTo | date : "dd/MM/yyyy" }})
              </div>
              <div class="metric-value text-light-text dark:text-dark-text">
                {{
                convertToVND(
                dataDashboard.statistics_average_cost_message
                .average_cost_this_period,
                26000
                )
                }}
              </div>
            </div>
            <div>
              <div class="text-neutral-content dark:text-dark-neutral-content mb-2">
                Overall Average Cost
              </div>
              <div class="metric-value text-light-text dark:text-dark-text">
                {{
                convertToVND(
                dataDashboard.statistics_average_cost_message.average_cost,
                26000
                )
                }}
              </div>
            </div>
          </div>
        </dx-card-content>
      </dx-card>
      }
    </div>
  </section>
</div>
<ng-template #dashboardStep1>
  <div>
    <p class="mb-3">Welcome to your DxConnect Dashboard! This is your command center where you can monitor all key metrics and activities at a glance.</p>
    <p class="mb-2">From here, you can:</p>
    <ul class="list-disc ml-5 text-sm">
      <li>View total conversations and their status</li>
      <li>Monitor AI performance metrics</li>
      <li>Track system usage and response times</li>
      <li>Access quick links to other modules</li>
    </ul>
  </div>
</ng-template>

<ng-template #dashboardStep2>
  <div>
    <p class="mb-3">This widget shows your Total Conversations in real-time. It displays:</p>
    <ul class="list-disc ml-5 text-sm mb-3">
      <li>Total number of conversations handled by your AI</li>
      <li>Breakdown by status (Open, Closed, Need Reply)</li>
      <li>Trend indicators showing increase/decrease</li>
      <li>Click to drill down into detailed conversation analytics</li>
    </ul>
  </div>
</ng-template>
<ng-template #dashboardStep3>
  <div>
    <p class="mb-3">Monitor your system usage and performance:</p>
    <ul class="list-disc ml-5 text-sm">
      <li><strong>Messages Used:</strong> Track your monthly message quota</li>
      <li><strong>Average Response Time:</strong> See how quickly your AI responds</li>
      <li><strong>Success Rate:</strong> Percentage of conversations resolved by AI</li>
      <li><strong>Peak Usage Times:</strong> Identify when your system is busiest</li>
    </ul>
  </div>
</ng-template>

<!-- Templates -->
<ng-template #templateNoteRef let-valuePercent="valuePercent">
  <div class="flex items-center">
    <span>
      @if (valuePercent > 0) {
      <svg width="16px" height="16px" fill="#22C55E">
        <use xlink:href="#arrow-trend-up"></use>
      </svg>
      } @if (valuePercent < 0) { <svg width="16px" height="16px" fill="#FF0000">
        <use xlink:href="#arrow-trend-down"></use>
        </svg>
        } @if (valuePercent === 0) {
        <svg width="16px" height="16px" fill="#212529BF">
          <use xlink:href="#arrow-left-right"></use>
        </svg>
        }
    </span>
    <span class="ml-2 mr-1" [ngStyle]="{ color: getColorText(valuePercent) }">{{ valuePercent }}%</span>
    <span>vs previous period</span>
  </div>
</ng-template>

<ng-template #templateChartTrendRef let-valuePercent="valuePercent">
  <div>
    @if (valuePercent > 0) {
    <svg width="100" height="60">
      <use xlink:href="#chart_trend_up"></use>
    </svg>
    } @if (valuePercent < 0) { <svg width="100" height="60">
      <use xlink:href="#chart_trend_down"></use>
      </svg>
      } @if (valuePercent === 0) {
      <svg width="100" height="60">
        <use></use>
      </svg>
      }
  </div>
</ng-template>
