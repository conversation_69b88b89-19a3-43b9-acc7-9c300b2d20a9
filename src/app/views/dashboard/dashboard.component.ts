import { CommonModule, DatePipe, NgStyle } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  ElementRef,
  inject,
  Inject,
  OnDestroy,
  OnInit,
  Optional,
  ViewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DATE_FORMATS, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TRIGGER_KEYS } from '@core/constants';
import { TriggerService } from '@core/services';
import { UIStore } from '@core/stores';
import { DxCard, DxCardContent, DxCardHeader } from '@dx-ui/ui';
import { MobileHeaderComponent } from '@shared/components';
import { IStatisticDashboard } from '@shared/models/dashboard.model';
import { HoursToHMSPipe } from '@shared/pipes/hours-to-hms.pipe';
import { DashboardService } from '@shared/services/dashboard.service';
import { ThemeService } from '@shared/services/theme.service';
import * as Highcharts from 'highcharts';
import moment from 'moment';
import { Subscription } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import {JoyrideModule} from 'ngx-joyride';
import {TourGuideService} from '@shared/services';

// Custom date formats to match the application's format
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'DD/MM/YYYY',
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgStyle,
    DatePipe,
    HoursToHMSPipe,
    MatDatepickerModule,
    MatNativeDateModule,
    MatFormFieldModule,
    MatInputModule,
    DxCard,
    DxCardHeader,
    DxCardContent,
    MobileHeaderComponent,
    JoyrideModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  providers: [{ provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }],
})
export class DashboardComponent implements OnInit, OnDestroy {
  @ViewChild('chartTotalConversationsRef')
  chartTotalConversationsRef?: ElementRef;
  @ViewChild('chartTransferredOptionsRef')
  chartTransferredOptionsRef?: ElementRef;
  @ViewChild('chartDomainStatisticsForWebWidgetChannel')
  chartDomainStatisticsForWebWidgetChannelRef?: ElementRef;
  @ViewChild('chartChannelMessageStatistics')
  chartChannelMessageStatisticsRef?: ElementRef;

  dataDashboard: IStatisticDashboard = {} as IStatisticDashboard;
  percentConversation = 0;
  percentMessPerConversation = 0;
  percentTransferred = 0;
  percentGeneratedMess = 0;
  activeDateRange: string = 'last30days'; // Default active date range
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  chartTransferredOptionsElm!: Highcharts.Chart;
  chartTotalConversationsElm!: Highcharts.Chart;
  chartDomainStatisticsForWebWidgetChannelElm!: Highcharts.Chart;
  chartChannelMessageStatisticsElm!: Highcharts.Chart;
  highcharts: typeof Highcharts = Highcharts;
  chartTransferredOptions: any = {
    chart: {
      type: 'pie',
      plotShadow: true,
      width: 120,
      height: 120,
      backgroundColor: null, // Will be set dynamically based on theme
    },
    title: {
      text: null,
    },
    subtitle: {
      useHTML: true,
      text: '',
      verticalAlign: 'middle',
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '',
      pointFormat: '<b>{point.y}</b>',
    },
    plotOptions: {
      pie: {
        minSize: 60,
        className: 'chart-pie',
        cursor: 'pointer',
        dataLabels: {
          enabled: false,
        },
        states: {
          hover: {
            halo: {
              size: 0,
            },
          },
        },
      },
    },
    colors: ['#2CAFFE', '#F2F4F7'],
    series: [
      {
        type: 'pie',
        innerSize: '75%',
        data: [],
        showInLegend: false,
      },
    ],
  };
  chartTotalConversations: any = {
    chart: {
      type: 'areaspline',
      height: 128,
      backgroundColor: null, // Will be set dynamically based on theme
    },
    title: {
      text: null,
    },
    xAxis: {
      tickLength: 0,
      type: 'datetime',
      lineWidth: 0,
      labels: {
        enabled: true,
        style: {
          color: null,
        },
      },
    },
    yAxis: {
      labels: {
        enabled: false,
      },
      title: {
        text: null,
      },
      tickInterval: 1,
      gridLineWidth: 1,
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      pointFormat: '{point.y}',
    },
    plotOptions: {
      series: {
        pointStart: Date.UTC(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate()
        ),
        pointIntervalUnit: 'day',
      },
    },
    series: [
      {
        name: 'Temperature',
        data: [],
        marker: {
          enabled: false,
        },
      },
    ],
  };
  chartTotalMessages = {
    chart: {
      type: 'areaspline',
      width: 740,
      height: 128,
    },
    title: {
      text: null,
    },
    xAxis: {
      tickLength: 0,
      type: 'datetime',
      lineWidth: 0,
    },
    yAxis: {
      labels: {
        enabled: false,
      },
      title: {
        text: null,
      },
      tickInterval: 1,
      gridLineWidth: 1,
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      pointFormat: '{point.y}',
    },
    plotOptions: {
      series: {
        pointStart: Date.UTC(
          new Date().getFullYear(),
          new Date().getMonth() - 1,
          new Date().getDate()
        ),
        pointIntervalUnit: 'day',
      },
    },
    series: [
      {
        name: 'Temperature',
        data: [
          0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 5, 1,
          0, 0, 0, 0, 0, 0, 0,
        ],
        marker: {
          enabled: false,
        },
      },
    ],
  };
  chartUserTalkIn = {
    chart: {
      type: 'pie',
      plotShadow: true,
      width: 480,
      height: 200,
    },
    title: {
      text: null,
    },
    legend: {
      layout: 'vertical',
      align: 'right',
      verticalAlign: 'top',
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '<b>{point.key}</b></br>',
      pointFormat: '{point.y}',
    },
    plotOptions: {
      pie: {
        minSize: 60,
        className: 'chart-pie',
        cursor: 'pointer',
        dataLabels: {
          enabled: false,
        },
        states: {
          hover: {
            halo: {
              size: 0,
            },
          },
        },
        showInLegend: true,
      },
    },
    series: [
      {
        type: 'pie',
        innerSize: '40%',
        data: [
          {
            name: 'User',
            y: 1,
          },
          {
            name: 'User2',
            y: 9,
          },
        ],
      },
    ],
  };
  chartUserTalkVia = {
    chart: {
      type: 'pie',
      plotShadow: true,
      width: 480,
      height: 200,
    },
    title: {
      text: null,
    },
    legend: {
      layout: 'vertical',
      align: 'right',
      verticalAlign: 'top',
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '<b>{point.key}</b></br>',
      pointFormat: '{point.y}',
    },
    plotOptions: {
      pie: {
        minSize: 60,
        className: 'chart-pie',
        cursor: 'pointer',
        dataLabels: {
          enabled: false,
        },
        states: {
          hover: {
            halo: {
              size: 0,
            },
          },
        },
        showInLegend: true,
      },
    },
    series: [
      {
        type: 'pie',
        innerSize: '60%',
        data: [
          {
            name: 'Preview',
            y: 10,
          },
        ],
      },
    ],
  };
  chartMessagePerWeek = {
    chart: {
      type: 'column',
    },
    title: {
      text: null,
    },
    xAxis: {
      tickLength: 0,
      lineWidth: 0,
      type: 'datetime',
      labels: {
        formatter: function (this: any) {
          return Highcharts.dateFormat('%a', this.value);
        },
      },
      align: 'center',
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of messages',
      },
      tickInterval: 2,
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '<b>{point.key}</b></br>',
      pointFormat: '{point.y}',
    },
    plotOptions: {
      series: {
        pointStart: Date.UTC(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate() - 7
        ),
        pointIntervalUnit: 'day',
      },
    },
    series: [
      {
        data: [0, 10, 0, 21, 0, 0, 0],
      },
    ],
  };
  chartMessagePerHour = {
    chart: {
      type: 'column',
    },
    title: {
      text: null,
    },
    xAxis: {
      tickLength: 0,
      lineWidth: 0,
      type: 'datetime',
      labels: {
        formatter: function (this: any) {
          return Highcharts.dateFormat('%H', this.value);
        },
      },
      align: 'center',
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of messages',
      },
      tickInterval: 2,
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '<b>{point.key}</b></br>',
      pointFormat: '{point.y}',
    },
    plotOptions: {
      series: {
        pointStart: Date.UTC(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate(),
          new Date().getHours() - 23
        ),
        pointInterval: 3600 * 1000,
      },
    },
    series: [
      {
        data: [
          0, 0, 0, 15, 2, 4, 10, 6, 0, 0, 0, 10, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0,
          0,
        ],
      },
    ],
  };

  channelMessageStatistics: any = {
    chart: {
      type: 'pie',
      plotShadow: true,
      width: null, // Let Highcharts handle the width dynamically
      height: null, // Let Highcharts handle the height dynamically
    },
    title: {
      text: null,
    },
    legend: {
      layout: 'vertical',
      align: 'right',
      verticalAlign: 'top',
      itemStyle: {
        color: null,
      },
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '<b>{point.key}</b></br>',
      pointFormat: '{point.y}',
    },
    plotOptions: {
      pie: {
        minSize: 40,
        className: 'chart-pie',
        cursor: 'pointer',
        dataLabels: {
          enabled: false,
        },
        states: {
          hover: {
            halo: {
              size: 0,
            },
          },
        },
        showInLegend: true,
      },
    },
    series: [
      {
        type: 'pie',
        innerSize: '60%',
        data: [],
      },
    ],
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 320,
          },
          chartOptions: {
            chart: {
              width: 320,
              height: 240,
            },
            legend: {
              align: 'center',
              verticalAlign: 'bottom',
            },
          },
        },
        {
          condition: {
            minWidth: 380,
          },
          chartOptions: {
            chart: {
              width: 380,
              height: 280,
            },
            legend: {
              align: 'right',
              verticalAlign: 'top',
            },
          },
        },
      ],
    },
  };
  domainStatisticsForWebWidgetChannel: any = {
    chart: {
      type: 'pie',
      plotShadow: true,
      width: null,
      height: null,
    },
    title: {
      text: null,
    },
    legend: {
      layout: 'vertical',
      align: 'right',
      verticalAlign: 'top',
      itemStyle: {
        color: null,
      },
    },
    credits: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      headerFormat: '<b>{point.key}</b></br>',
      pointFormat: '{point.y}',
    },
    plotOptions: {
      pie: {
        minSize: 40,
        className: 'chart-pie',
        cursor: 'pointer',
        dataLabels: {
          enabled: false,
        },
        states: {
          hover: {
            halo: {
              size: 0,
            },
          },
        },
        showInLegend: true,
      },
    },
    series: [
      {
        type: 'pie',
        innerSize: '60%',
        data: [],
      },
    ],
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 320,
          },
          chartOptions: {
            chart: {
              width: 320,
              height: 240,
            },
            legend: {
              align: 'center',
              verticalAlign: 'bottom',
            },
          },
        },
        {
          condition: {
            minWidth: 380,
          },
          chartOptions: {
            chart: {
              width: 380,
              height: 280,
            },
            legend: {
              align: 'right',
              verticalAlign: 'top',
            },
          },
        },
      ],
    },
  };

  // Store both Moment objects and Date objects for compatibility
  timeFromMoment = moment().subtract(30, 'days');
  timeToMoment = moment();
  timeNowMoment = moment();

  // Convert Moment objects to Date objects for use with date pipe
  timeFrom = this.timeFromMoment.toDate();
  timeTo = this.timeToMoment.toDate();
  timeNow = this.timeNowMoment.toDate();
  reloadAfterChangeAiSubscription: Subscription;
  dataDomainStatisticWeb: any;
  dataMessageStatistics: any;

  // Use signal for theme instead of subscription
  currentTheme: any; // Will be initialized in constructor

  uiStore = inject(UIStore);
  private triggerService = inject(TriggerService);
  private readonly tourGuideService = inject(TourGuideService);
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: { aiId: string } | null,
    @Optional() public dialogRef: MatDialogRef<DashboardComponent>,
    private dashboardService: DashboardService,
    private themeService: ThemeService
  ) {
    // Initialize the theme signal
    this.currentTheme = this.themeService.theme;

    this.reloadAfterChangeAiSubscription = (
      window as any
    ).CommonUtilsService?.reloadAfterChangeAi$?.subscribe((value: any) => {
      this.callAPIStats();
    });

    effect(() => {
      const trigger = this.triggerService.get(TRIGGER_KEYS.RE_INIT_PREVIEW)();
      if (trigger) {
        this.callAPIStats();
      }
    });

    // Use effect to react to theme changes
    effect(() => {
      // This will automatically run when the theme signal changes
      this.updateChartsForTheme();
    });
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const from = params['from'];
      const to = params['to'];
      if (from && to) {
        this.timeFromMoment = moment(from, 'YYYY-MM-DD').startOf('day');
        this.timeToMoment = moment(to, 'YYYY-MM-DD').endOf('day');
        this.timeFrom = this.timeFromMoment.toDate();
        this.timeTo = this.timeToMoment.toDate();
        this.activeDateRange = '';
        this.callAPIStats();
      } else {
        // Nếu không có param thì dùng mặc định
        this.applyDateRange('last30days');
      }
    });

    // Configure tour guide
    this.tourGuideService.configureTour({
      steps: ['dashboard-step-1', 'dashboard-step-2', 'dashboard-step-3'],
      priority: 10,
      dependsOn: ['main_nav'] // Phải đợi main-layout tour hoàn thành
    });
  }
  /**
   * Date class function for the calendar to highlight date ranges
   */
  dateClass = (date: Date): string => {
    if (!date || !this.timeFrom || !this.timeTo) return '';

    // Highlight dates in the selected range
    const dateValue = date.getTime();
    const fromDate = this.timeFrom.getTime();
    const toDate = this.timeTo.getTime();

    if (dateValue >= fromDate && dateValue <= toDate) {
      return 'date-in-range';
    }

    return '';
  };

  /**
   * Handle date selection from the calendar
   */
  onDateSelected(date: Date | null): void {
    if (!date) return;

    // Update the date range based on the selected date
    this.timeFromMoment = moment(date).startOf('day');
    this.timeToMoment = moment(date).endOf('day');

    // Update Date objects
    this.timeFrom = this.timeFromMoment.toDate();
    this.timeTo = this.timeToMoment.toDate();

    // Reset active date range since we're using custom dates
    this.activeDateRange = '';

    // Refresh data
    this.callAPIStats();
  }

  /* Example data for testing
  initTestData(): void {
    this.dataDashboard = {
      conversation_stats: {
        conversations: 11,
        conversations_by_day: {
          "2024-01-07": 0,
          "2024-01-08": 0,
          "2024-01-09": 0,
        },
        conversations_handoff: 1
      },
      start_date: "2023-12-20",
      end_date: "2023-12-25",
      messages_limit: 100,
      messages_this_period: 29
    };
    this.dataDashboard.percentMessagesPeriod = parseFloat((this.dataDashboard.messages_this_period / this.dataDashboard.messages_limit * 100).toFixed(0));

    this.chartTransferredOptions.series[0].data = [this.dataDashboard.conversation_stats.conversations_handoff, this.dataDashboard.conversation_stats.conversations - this.dataDashboard.conversation_stats.conversations_handoff];
    const percentConversationsHandoff = parseFloat((this.dataDashboard.conversation_stats.conversations_handoff / this.dataDashboard.conversation_stats.conversations * 100).toFixed(1));
    this.chartTransferredOptions.subtitle.text = `<span style="font-size: 14px; color: #2CAFFE;">${percentConversationsHandoff}%</span>`;
  }*/

  ngOnDestroy() {
    if (this.reloadAfterChangeAiSubscription) {
      this.reloadAfterChangeAiSubscription.unsubscribe();
    }
  }

  callAPIStats(): void {
    const body: any = {
      start_date: this.timeFromMoment.format('YYYY-MM-DD'),
      end_date: this.timeToMoment.format('YYYY-MM-DD'),
    };

    if (this.data) {
      body.ai_id = this.data.aiId.toString();
    }

    // Apply theme to chart configurations
    this.applyThemeToChartConfigs();

    this.chartTotalConversations.plotOptions.series.pointStart = Date.UTC(
      this.timeFromMoment.year(),
      this.timeFromMoment.month(),
      this.timeFromMoment.date()
    );
    this.dashboardService.getStatisticalDashboard(body).subscribe({
      next: (res) => {
        this.dataDashboard = res;
        if (
          this.dataDashboard.messages_this_period !== undefined &&
          this.dataDashboard.messages_limit !== undefined
        ) {
          this.dataDashboard.percentMessagesPeriod = parseFloat(
            (
              (this.dataDashboard.messages_this_period /
                (this.dataDashboard.messages_limit || 1)) *
              100
            ).toFixed(2)
          );
        }

        let percentConversationsHandoff = 0;
        if (
          this.dataDashboard.conversation_stats &&
          this.dataDashboard.conversation_stats.conversations_handoff !==
            undefined &&
          this.dataDashboard.conversation_stats.conversations !== undefined
        ) {
          percentConversationsHandoff = parseFloat(
            (
              (this.dataDashboard.conversation_stats.conversations_handoff /
                (this.dataDashboard.conversation_stats.conversations === 0
                  ? 1
                  : this.dataDashboard.conversation_stats.conversations)) *
              100
            ).toFixed(2)
          );
        }

        this.chartTransferredOptions.subtitle.text = `<span style="font-size: 14px; color: #2CAFFE;">${percentConversationsHandoff}%</span>`;

        if (
          this.dataDashboard.conversation_stats &&
          this.dataDashboard.conversation_stats.conversations_handoff !==
            undefined &&
          this.dataDashboard.conversation_stats.conversations !== undefined
        ) {
          this.chartTransferredOptions.series[0].data = [
            this.dataDashboard.conversation_stats.conversations_handoff || 0,
            (this.dataDashboard.conversation_stats.conversations || 0) -
              (this.dataDashboard.conversation_stats.conversations_handoff ||
                0),
          ];
        } else {
          this.chartTransferredOptions.series[0].data = [0, 0];
        }

        this.chartTransferredOptions = { ...this.chartTransferredOptions };

        if (this.chartTransferredOptionsRef) {
          const highChartElement =
            this.chartTransferredOptionsRef.nativeElement;
          this.chartTransferredOptionsElm = Highcharts.chart(
            highChartElement,
            this.chartTransferredOptions
          );
        }

        if (
          this.dataDashboard.statistics_conversation_domain?.list_conv_embed
        ) {
          this.dataDomainStatisticWeb = Object.entries(
            this.dataDashboard.statistics_conversation_domain.list_conv_embed
          ).map(([name, y]) => ({ name, y }));
          this.domainStatisticsForWebWidgetChannel.series[0].data =
            this.dataDomainStatisticWeb;

          if (this.chartDomainStatisticsForWebWidgetChannelRef) {
            const highChartElement =
              this.chartDomainStatisticsForWebWidgetChannelRef.nativeElement;
            this.chartDomainStatisticsForWebWidgetChannelElm = Highcharts.chart(
              highChartElement,
              this.domainStatisticsForWebWidgetChannel
            );
          }
        }
        if (
          this.dataDashboard.statistics_conversation_domain
            ?.list_conv_integration
        ) {
          this.dataMessageStatistics = Object.entries(
            this.dataDashboard.statistics_conversation_domain
              .list_conv_integration
          ).map(([name, y]) => ({ name, y }));
          this.channelMessageStatistics.series[0].data =
            this.dataMessageStatistics;

          if (this.chartChannelMessageStatisticsRef) {
            const highChartElement =
              this.chartChannelMessageStatisticsRef.nativeElement;
            this.chartChannelMessageStatisticsElm = Highcharts.chart(
              highChartElement,
              this.channelMessageStatistics
            );
          }
        }

        if (this.dataDashboard.conversation_stats?.conversations_by_day) {
          this.chartTotalConversations.series[0].data = Object.keys(
            this.dataDashboard.conversation_stats.conversations_by_day
          ).map(
            (key) =>
              this.dataDashboard.conversation_stats?.conversations_by_day?.[
                key
              ] || 0
          );
          this.chartTotalConversations = { ...this.chartTotalConversations };
          if (this.chartTotalConversationsRef) {
            const highChartElement =
              this.chartTotalConversationsRef.nativeElement;
            this.chartTotalConversationsElm = Highcharts.chart(
              highChartElement,
              this.chartTotalConversations
            );
          }
        }
      },
    });
  }

  getColorText(value: number) {
    if (value > 0) {
      return '#22C55E';
    } else if (value < 0) {
      return '#FF0000';
    } else {
      return '#212529BF';
    }
  }

  onDateFromChange(event: any) {
    // Parse the date from the input value (format: dd/MM/yyyy)
    const dateValue = event.target.value;
    if (dateValue) {
      const [day, month, year] = dateValue.split('/');
      // Month is 0-indexed in JavaScript Date
      this.timeFromMoment = moment(
        new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
      );
      this.timeFrom = this.timeFromMoment.toDate();
      this.callAPIStats();

      // Reset active date range since we're using custom dates
      this.activeDateRange = '';
    }
  }

  onDateToChange(event: any) {
    // Parse the date from the input value (format: dd/MM/yyyy)
    const dateValue = event.target.value;
    if (dateValue) {
      const [day, month, year] = dateValue.split('/');
      // Month is 0-indexed in JavaScript Date
      this.timeToMoment = moment(
        new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
      );
      this.timeTo = this.timeToMoment.toDate();
      this.callAPIStats();

      // Reset active date range since we're using custom dates
      this.activeDateRange = '';
    }
  }

  /**
   * Handle date change from Material datepicker (From date)
   */
  onDateFromMatChange(event: any) {
    if (event && event.value) {
      // Update the from date
      this.timeFromMoment = moment(event.value).startOf('day');
      this.timeFrom = this.timeFromMoment.toDate();

      // Reset active date range since we're using custom dates
      this.activeDateRange = '';

      // Refresh data
      this.callAPIStats();
      this.updateDateParams();
    }
  }

  updateDateParams() {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        from: this.timeFromMoment.format('YYYY-MM-DD'),
        to: this.timeToMoment.format('YYYY-MM-DD'),
      },
      queryParamsHandling: 'merge',
    });
  }

  /**
   * Handle date change from Material datepicker (To date)
   */
  onDateToMatChange(event: any) {
    if (event && event.value) {
      // Update the to date
      this.timeToMoment = moment(event.value).endOf('day');
      this.timeTo = this.timeToMoment.toDate();

      // Reset active date range since we're using custom dates
      this.activeDateRange = '';

      // Refresh data
      this.callAPIStats();
    }
  }

  /**
   * Apply a predefined date range
   * @param range The range to apply ('last7days' or 'last30days')
   */
  applyDateRange(range: string) {
    const now = moment();

    switch (range) {
      case 'today':
        this.timeFromMoment = moment().startOf('day');
        this.timeToMoment = moment().endOf('day');
        break;
      case 'last7days':
        this.timeFromMoment = moment().subtract(7, 'days');
        this.timeToMoment = now;
        break;
      case 'last30days':
        this.timeFromMoment = moment().subtract(30, 'days');
        this.timeToMoment = now;
        break;
      default:
        return;
    }

    // Set active date range for UI highlighting
    this.activeDateRange = range;

    // Update Date objects
    this.timeFrom = this.timeFromMoment.toDate();
    this.timeTo = this.timeToMoment.toDate();

    // Refresh data
    this.callAPIStats();

    // No need to reinitialize for Material datepicker
  }

  convertToVND(
    amountInUSD: number | undefined,
    exchangeRate: number = 24000
  ): string {
    if (amountInUSD == null || isNaN(amountInUSD)) {
      return 'N/A';
    }
    const amountInVND = Math.round(amountInUSD * exchangeRate);
    if (amountInVND >= 1_000_000_000) {
      return `${(amountInVND / 1_000_000_000).toFixed(1)}B ₫`;
    } else if (amountInVND >= 1_000_000) {
      return `${(amountInVND / 1_000_000).toFixed(1)}M ₫`;
    }

    return `${amountInVND.toLocaleString('en-EN')} ₫`;
  }

  getConversationPercentage(): number {
    // Calculate the percentage change in conversations compared to previous period
    // Return 0 if data is not available
    if (!this.dataDashboard?.conversation_stats?.percentage_conversation_previous_period) {
      return 0;
    }
    return parseFloat(
      this.dataDashboard.conversation_stats.percentage_conversation_previous_period.toFixed(
        1
      )
    );
  }

  getHandoffPercentage(): number {
    // Calculate the percentage change in handoffs compared to previous period
    // Return 0 if data is not available
    if (
      !this.dataDashboard?.conversation_stats
        ?.conversations_handoff_percent_change
    ) {
      return 0;
    }
    return parseFloat(
      this.dataDashboard.conversation_stats.conversations_handoff_percent_change.toFixed(
        1
      )
    );
  }

  /**
   * Gets CSS variable value from document root
   * @param variableName CSS variable name (without --)
   * @param fallback Fallback value if variable not found
   * @returns CSS variable value or fallback
   */
  private getCSSVariable(variableName: string, fallback: string): string {
    const documentStyle = getComputedStyle(document.documentElement);
    return (
      documentStyle.getPropertyValue(`--${variableName}`).trim() || fallback
    );
  }

  /**
   * Gets theme colors based on current theme
   * @returns Object containing all theme colors
   */
  private getThemeColors() {
    const isDarkTheme = this.uiStore.theme() === 'dark';

    return {
      backgroundColor: isDarkTheme
        ? this.getCSSVariable('color-dark-base-200', '#1E232E')
        : this.getCSSVariable('color-base-200', '#ffffff'),
      textColor: isDarkTheme
        ? this.getCSSVariable('color-dark-text', '#FFFFFF')
        : this.getCSSVariable('color-light-text', '#000000'),
      gridColor: isDarkTheme ? '#3e3e3e' : '#e9e9e9',
      axisLineColor: isDarkTheme ? '#1E232E' : '#EBEBEF',
    };
  }

  /**
   * Updates chart configurations based on the current theme
   */
  updateChartsForTheme(): void {
    if (!this.currentTheme) return;

    const { backgroundColor, textColor, gridColor, axisLineColor } =
      this.getThemeColors();

    // Update chartTotalConversations
    if (this.chartTotalConversations) {
      this.chartTotalConversations.chart.backgroundColor = backgroundColor;
      this.chartTotalConversations.yAxis.gridLineColor = gridColor;
      this.chartTotalConversations.yAxis.lineColor = axisLineColor;
      if (!this.chartTotalConversations.xAxis.labels.style) {
        this.chartTotalConversations.xAxis.labels.style = {};
      }
      this.chartTotalConversations.xAxis.labels.style.color = textColor;
      this.chartTotalConversations.xAxis.lineColor = axisLineColor;

      // Update title style if exists
      if (this.chartTotalConversations.title) {
        if (!this.chartTotalConversations.title.style) {
          this.chartTotalConversations.title.style = {};
        }
        this.chartTotalConversations.title.style.color = textColor;
      }

      // Re-render chart if already initialized
      if (this.chartTotalConversationsRef && this.chartTotalConversationsElm) {
        this.chartTotalConversationsElm = Highcharts.chart(
          this.chartTotalConversationsRef.nativeElement,
          this.chartTotalConversations
        );
      }
    }

    // Update chartTransferredOptions
    if (this.chartTransferredOptions) {
      this.chartTransferredOptions.chart.backgroundColor = backgroundColor;

      // Update subtitle color if exists
      if (
        this.chartTransferredOptions.subtitle &&
        this.chartTransferredOptions.subtitle.text
      ) {
        // Keep the existing percentage color but update background
        const currentSubtitle = this.chartTransferredOptions.subtitle.text;
        this.chartTransferredOptions.subtitle.text = currentSubtitle;
      }

      // Re-render chart if already initialized
      if (this.chartTransferredOptionsRef && this.chartTransferredOptionsElm) {
        this.chartTransferredOptionsElm = Highcharts.chart(
          this.chartTransferredOptionsRef.nativeElement,
          this.chartTransferredOptions
        );
      }
    }

    // Update domainStatisticsForWebWidgetChannel
    if (this.domainStatisticsForWebWidgetChannel) {
      this.domainStatisticsForWebWidgetChannel.chart.backgroundColor =
        backgroundColor;

      // Update legend style
      if (this.domainStatisticsForWebWidgetChannel.legend) {
        if (!this.domainStatisticsForWebWidgetChannel.legend.itemStyle) {
          this.domainStatisticsForWebWidgetChannel.legend.itemStyle = {};
        }
        this.domainStatisticsForWebWidgetChannel.legend.itemStyle.color =
          textColor;
      }

      // Update title style if exists
      if (this.domainStatisticsForWebWidgetChannel.title) {
        if (!this.domainStatisticsForWebWidgetChannel.title.style) {
          this.domainStatisticsForWebWidgetChannel.title.style = {};
        }
        this.domainStatisticsForWebWidgetChannel.title.style.color = textColor;
      }

      // Re-render chart if already initialized
      if (
        this.chartDomainStatisticsForWebWidgetChannelRef &&
        this.chartDomainStatisticsForWebWidgetChannelElm
      ) {
        this.chartDomainStatisticsForWebWidgetChannelElm = Highcharts.chart(
          this.chartDomainStatisticsForWebWidgetChannelRef.nativeElement,
          this.domainStatisticsForWebWidgetChannel
        );
      }
    }

    // Update channelMessageStatistics
    if (this.channelMessageStatistics) {
      this.channelMessageStatistics.chart.backgroundColor = backgroundColor;

      // Update legend style
      if (this.channelMessageStatistics.legend) {
        if (!this.channelMessageStatistics.legend.itemStyle) {
          this.channelMessageStatistics.legend.itemStyle = {};
        }
        this.channelMessageStatistics.legend.itemStyle.color = textColor;
      }

      // Update title style if exists
      if (this.channelMessageStatistics.title) {
        if (!this.channelMessageStatistics.title.style) {
          this.channelMessageStatistics.title.style = {};
        }
        this.channelMessageStatistics.title.style.color = textColor;
      }

      // Re-render chart if already initialized
      if (
        this.chartChannelMessageStatisticsRef &&
        this.chartChannelMessageStatisticsElm
      ) {
        this.chartChannelMessageStatisticsElm = Highcharts.chart(
          this.chartChannelMessageStatisticsRef.nativeElement,
          this.channelMessageStatistics
        );
      }
    }

    // Update other charts if they exist
    this.updateAdditionalChartsForTheme(
      backgroundColor,
      textColor,
      gridColor,
      axisLineColor
    );
  }

  /**
   * Updates additional charts for theme changes
   * @param backgroundColor Background color for charts
   * @param textColor Text color for charts
   * @param gridColor Grid line color
   * @param axisLineColor Axis line color
   */
  private updateAdditionalChartsForTheme(
    backgroundColor: string,
    textColor: string,
    gridColor: string,
    axisLineColor: string
  ): void {
    // Update chartTotalMessages if it exists
    if (this.chartTotalMessages && this.chartTotalMessages.chart) {
      // Add backgroundColor property safely
      (this.chartTotalMessages.chart as any).backgroundColor = backgroundColor;

      // Add missing properties safely to yAxis
      if (this.chartTotalMessages.yAxis) {
        (this.chartTotalMessages.yAxis as any).gridLineColor = gridColor;
        (this.chartTotalMessages.yAxis as any).lineColor = axisLineColor;
      }

      // Add missing properties safely to xAxis
      if (this.chartTotalMessages.xAxis) {
        const xAxis = this.chartTotalMessages.xAxis as any;
        if (!xAxis.labels) {
          xAxis.labels = { style: { color: textColor } };
        } else if (!xAxis.labels.style) {
          xAxis.labels.style = { color: textColor };
        } else {
          xAxis.labels.style.color = textColor;
        }
        xAxis.lineColor = axisLineColor;
      }
    }

    // Update chartUserTalkIn if it exists (pie chart - no yAxis/xAxis)
    if (this.chartUserTalkIn && this.chartUserTalkIn.chart) {
      (this.chartUserTalkIn.chart as any).backgroundColor = backgroundColor;

      // Update legend if exists
      if (this.chartUserTalkIn.legend) {
        if (!(this.chartUserTalkIn.legend as any).itemStyle) {
          (this.chartUserTalkIn.legend as any).itemStyle = { color: textColor };
        } else {
          (this.chartUserTalkIn.legend as any).itemStyle.color = textColor;
        }
      }
    }

    // Update chartUserTalkVia if it exists (pie chart - no yAxis/xAxis)
    if (this.chartUserTalkVia && this.chartUserTalkVia.chart) {
      (this.chartUserTalkVia.chart as any).backgroundColor = backgroundColor;

      // Update legend if exists
      if (this.chartUserTalkVia.legend) {
        if (!(this.chartUserTalkVia.legend as any).itemStyle) {
          (this.chartUserTalkVia.legend as any).itemStyle = {
            color: textColor,
          };
        } else {
          (this.chartUserTalkVia.legend as any).itemStyle.color = textColor;
        }
      }
    }

    // Update chartMessagePerWeek if it exists
    if (this.chartMessagePerWeek && this.chartMessagePerWeek.chart) {
      (this.chartMessagePerWeek.chart as any).backgroundColor = backgroundColor;

      if (this.chartMessagePerWeek.yAxis) {
        (this.chartMessagePerWeek.yAxis as any).gridLineColor = gridColor;
      }

      if (this.chartMessagePerWeek.xAxis) {
        const xAxis = this.chartMessagePerWeek.xAxis as any;
        if (xAxis.labels) {
          if (!xAxis.labels.style) {
            xAxis.labels.style = { color: textColor };
          } else {
            xAxis.labels.style.color = textColor;
          }
        }
      }
    }

    // Update chartMessagePerHour if it exists
    if (this.chartMessagePerHour && this.chartMessagePerHour.chart) {
      (this.chartMessagePerHour.chart as any).backgroundColor = backgroundColor;

      if (this.chartMessagePerHour.yAxis) {
        (this.chartMessagePerHour.yAxis as any).gridLineColor = gridColor;
      }

      if (this.chartMessagePerHour.xAxis) {
        const xAxis = this.chartMessagePerHour.xAxis as any;
        if (xAxis.labels) {
          if (!xAxis.labels.style) {
            xAxis.labels.style = { color: textColor };
          } else {
            xAxis.labels.style.color = textColor;
          }
        }
      }
    }
  }

  /**
   * Applies theme colors to chart configurations before rendering
   * This method should be called before chart initialization
   */
  private applyThemeToChartConfigs(): void {
    if (!this.currentTheme) return;

    const { backgroundColor, textColor, gridColor } = this.getThemeColors();

    // Apply theme to chartTotalConversations
    if (this.chartTotalConversations) {
      this.chartTotalConversations.chart.backgroundColor = backgroundColor;
      if (this.chartTotalConversations.yAxis) {
        this.chartTotalConversations.yAxis.gridLineColor = gridColor;
      }
      if (
        this.chartTotalConversations.xAxis &&
        this.chartTotalConversations.xAxis.labels
      ) {
        if (!this.chartTotalConversations.xAxis.labels.style) {
          this.chartTotalConversations.xAxis.labels.style = {};
        }
        this.chartTotalConversations.xAxis.labels.style.color = textColor;
      }
    }

    // Apply theme to chartTransferredOptions
    if (this.chartTransferredOptions) {
      this.chartTransferredOptions.chart.backgroundColor = backgroundColor;
    }

    // Apply theme to domainStatisticsForWebWidgetChannel
    if (this.domainStatisticsForWebWidgetChannel) {
      this.domainStatisticsForWebWidgetChannel.chart.backgroundColor =
        backgroundColor;
      if (this.domainStatisticsForWebWidgetChannel.legend) {
        if (!this.domainStatisticsForWebWidgetChannel.legend.itemStyle) {
          this.domainStatisticsForWebWidgetChannel.legend.itemStyle = {};
        }
        this.domainStatisticsForWebWidgetChannel.legend.itemStyle.color =
          textColor;
      }
    }

    // Apply theme to channelMessageStatistics
    if (this.channelMessageStatistics) {
      this.channelMessageStatistics.chart.backgroundColor = backgroundColor;
      if (this.channelMessageStatistics.legend) {
        if (!this.channelMessageStatistics.legend.itemStyle) {
          this.channelMessageStatistics.legend.itemStyle = {};
        }
        this.channelMessageStatistics.legend.itemStyle.color = textColor;
      }
    }
  }

  get handoffPercentage(): number | undefined {
    return this.dataDashboard?.conversation_stats
      ?.percentage_conversations_handoff_previous_period;
  }
}
