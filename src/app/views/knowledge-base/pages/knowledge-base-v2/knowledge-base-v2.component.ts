import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  ElementRef,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  Renderer2,
  signal,
  viewChild,
  viewChildren,
} from '@angular/core';
import {
  FormArray,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { UIStore, UserAiStore } from '@core/stores';

import { SelectionModel } from '@angular/cdk/collections';
import { OverlayModule } from '@angular/cdk/overlay';
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput, DxLabel,
  DxOption,
  DxPrefix, DxProgressSpinner,
  DxSelect,
} from '@dx-ui/ui';
import {
  BaseComponent,
  ConfirmDialogComponent,
  MobileDrawerComponent,
  MobileHeaderComponent,
  SvgIconComponent,
} from '@shared/components';

import { IFile, IFolder, IFolderFilter, ISearchModel } from '@shared/models';
import {
  CollectionsService,
  FileFolderService,
  KnowledgeBaseService,
  SettingsService,
  SocketService,
} from '@shared/services';

// Define interfaces locally since they don't exist in shared models

export interface ICollection {
  id: number;
  name: string;
}

interface IUser {
  id: number;
  email: string;
}

import {ClickOutsideDirective, MHeaderLeftDirective, MHeaderRightDirective} from '@shared/directives';
import { CharFirstPipe } from '@shared/pipes/char-first.pipe';
import { AddOrEditFileComponent } from '@views/knowledge-base/pages/knowledge-base-v2/add-or-edit-file/add-or-edit-file.component';
import { AddOrEditFolderComponent } from '@views/knowledge-base/pages/knowledge-base-v2/add-or-edit-folder/add-or-edit-folder.component';
import { ImportUrlComponent } from '@views/knowledge-base/pages/knowledge-base-v2/import-url/import-url.component';
import { UploadFileComponent } from '@views/knowledge-base/pages/knowledge-base-v2/upload-file/upload-file.component';
import { ViewOrEditContentFileComponent } from '@views/knowledge-base/pages/knowledge-base-v2/view-or-edit-content-file/view-or-edit-content-file.component';
import { Subscription } from 'rxjs';
import { GridViewComponent } from './components/grid-view/grid-view.component';
import { ListViewComponent } from './components/list-view/list-view.component';
import { APP_ROUTES } from '@core/constants';
import {MoveFileComponent} from '@views/knowledge-base/pages/knowledge-base-v2/move-file/move-file.component';
import {JoyrideModule} from 'ngx-joyride';
import {TourGuideService} from '@shared/services';

@Component({
  selector: 'app-knowledge-base-v2',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatChipsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSlideToggleModule,
    OverlayModule,
    ScrollingModule,
    GridViewComponent,
    ListViewComponent,
    DxFormField,
    DxSelect,
    DxOption,
    DxInput,
    SvgIconComponent,
    DxPrefix,
    ClickOutsideDirective,
    DxButton,
    CharFirstPipe,
    MobileHeaderComponent,
    MobileDrawerComponent,
    MHeaderRightDirective,
    DxLabel,
    DxProgressSpinner,
    MHeaderLeftDirective,
    JoyrideModule
  ],
  templateUrl: './knowledge-base-v2.component.html',
  styleUrl: './knowledge-base-v2.component.css',
})
export class KnowledgeBaseV2Component
  extends BaseComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  // Add signal declaration
  isEditingContent = signal<boolean>(false);

  // ViewChild references (Angular 19 signals)
  assignPermissionFolderDialog = viewChild<any>('assignPermissionFolderDialog');
  virtualScrollViewports = viewChildren(CdkVirtualScrollViewport);
  openCreateMenu = signal(false);

  uiStore = inject(UIStore);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private dialogService = inject(DxDialog);
  private renderer2 = inject(Renderer2);
  private userAiStore = inject(UserAiStore);
  private KnowledgeBaseService = inject(KnowledgeBaseService);
  private fileFolderService = inject(FileFolderService);
  private collectionsService = inject(CollectionsService);
  private settingsService = inject(SettingsService);
  private socketService = inject(SocketService);
  private tourGuideService = inject(TourGuideService);

  // Permissions and roles
  listPermissions = [
    { label: 'Read only', value: 1 },
    { label: 'Write', value: 2 },
    { label: 'Delete', value: 4 },
  ];
  permissions: any;
  parent_folder_permissions: any;
  roleUserInAI: any;
  backFn = () => {
    this.router.navigate([APP_ROUTES.MENU]);
  }
  closeFn = () => {
    this.viewFilter.set(false);
  }

  // State management (signals)
  showStyle = signal<'grid' | 'list'>('list');
  searchingMode = signal(false);
  isLoadingMore = signal(false);
  hasMoreData = signal(false);
  isNavigatingToFolder = signal(false);
  parentId = signal<number | null>(null);

  // Data (signals)
  folderList = signal<IFolder[]>([]);
  fileList = signal<IFile[]>([]);
  combinedList = signal<(IFolder | IFile)[]>([]);
  breadcrumbList = signal<IFolder[]>([]);
  collectionList = signal<ICollection[]>([]);
  listUserInAI = signal<IUser[]>([]);
  listUserInAIAllowed = signal<IUser[]>([]);

  // File and folder operations (signals)
  collectionData = signal<any>(null);
  collectionOfFile = signal<any>(null);
  metadata = signal<any>(null);
  rootURL = signal('');
  modeGetUrl = signal('getOne');
  breadcrumbs = signal<IFolder[]>([]);

  // Getters for template binding
  get rootURLValue() {
    return this.rootURL();
  }
  set rootURLValue(value: string) {
    this.rootURL.set(value);
  }

  get metadataValue() {
    return this.metadata();
  }
  set metadataValue(value: any) {
    this.metadata.set(value);
  }

  get fileContentValue() {
    return this.fileContent();
  }
  set fileContentValue(value: string) {
    this.fileContent.set(value);
  }

  get collectionOfFileValue() {
    return this.collectionOfFile();
  }
  set collectionOfFileValue(value: any) {
    this.collectionOfFile.set(value);
  }

  // Loading states (signals)
  isImportingUrl = signal(false);
  isSavingPermission = signal(false);

  // Forms
  assignFolderForm!: FormGroup;

  // Move file dialog (signals)
  foldersCanMoveTo = signal<IFolder[]>([]);

  // Utility
  timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  hideSingleSelectionIndicator = signal(false);

  // Selection models
  folderSelection = new SelectionModel<IFolder>(true, []);
  fileSelection = new SelectionModel<IFile>(true, []);
  userSelection = new SelectionModel<any>(true, []);

  // Keyboard state (signals)
  isShiftPressed = signal(false);
  isCtrlPressed = signal(false);

  // Enhanced selection state for Windows File Explorer behavior
  lastSelectedItem = signal<{ item: IFolder | IFile; type: 'folder' | 'file'; index: number } | null>(null);

  // Range anchor - persistent anchor point for range selection (Windows Explorer behavior)
  rangeAnchor = signal<{ item: IFolder | IFile; type: 'folder' | 'file'; index: number } | null>(null);

  // Track Shift key state transitions for anchor persistence
  shiftWasReleased = signal(true); // Start as true to allow first anchor setting

  // Edit state (signals)
  isEditing = signal(false);

  // Context menu selected items (signals)
  selectedFolder = signal<IFolder | null>(null);
  selectedFile = signal<IFile | null>(null);

  // Dialog data
  fileRename: any = {
    file_id: null,
    new_name: '',
  };

  // File content viewing (signals)
  fileContent = signal('');

  // Search and filter
  override searchModel: ISearchModel = {
    name: '',
    collection_id: 0,
    folder_id: 0,
    file_type: '',
    file_status: '',
    order: 'DESC',
    sort_by: 'updated_at',
    page: 1,
    per_page: 50,
    id: 0,
  };

  // File type and status options
  listFileType = [
    { label: 'All', value: '' },
    { label: 'PDF', value: 'PDF' },
    { label: 'CSV', value: 'CSV' },
    { label: 'TXT', value: 'TXT' },
    { label: 'URL', value: 'URL' },
    { label: 'Markdown', value: 'MD' },
  ];

  listStatus = [
    { label: 'All', value: '' },
    { label: 'Pending', value: 'PENDING' },
    { label: 'In progress', value: 'IN_PROGRESS' },
    { label: 'Completed', value: 'COMPLETED' },
  ];

  private subscription = new Subscription();
  private searchTimeout: any;

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.searchModel.name = params['name'] || '';
      this.searchModel.file_type = params['file_type'] || '';
      this.searchModel.file_status = params['file_status'] || '';
      this.applyFilter(false); // Không update param khi khởi tạo
      this.onSearchChange(this.searchModel.name, false);
    });
    // Initialize user role
    this.roleUserInAI = this.userAiStore.currentAi()?.role;

    // Initialize forms
    this.assignFolderForm = this.fb.group({
      folder_ids: [[], [Validators.required]],
      assign_permissions: this.fb.array([], Validators.required),
    });

    // Load initial data
    this.getListUserInAi();
    this.getCollections();
    this.listenFileUpdateSocket();

    // Configure tour guide
    this.tourGuideService.configureTour({
      steps: ['knb_header', 'knb_view'],
      priority: 10,
      dependsOn: ['main_nav']
    });
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        const numericId = parseInt(id, 10);
        const folder: IFolder = {
          id: numericId,
          ai_id: null as any,
          parent_id: null,
          name: null as any,
          users: [],
          isDeleting: null as any,
        };
        this.toFolder(folder, 'auto');
        this.fileFolderService.getBreadCrumb({ id: numericId }).subscribe({
          next: (res) => {
            this.breadcrumbs.set(res.reverse());
          },
          error: () => {
            this.breadcrumbs.set([]);
          },
        });
      } else {
        this.viewFolder({
          id: 0,
          name: '',
          collection_id: 0,
          folder_id: 0,
          file_type: '',
          file_status: '',
          sort_by: 'updated_at',
          order: 'DESC',
          page: 1,
          per_page: 50,
        });
      }
    });
  }

  updateAllFilterParams() {
    const params: any = {};
    if (this.searchModel.name) params.name = this.searchModel.name;
    if (this.searchModel.file_type) params.file_type = this.searchModel.file_type;
    if (this.searchModel.file_status) params.file_status = this.searchModel.file_status;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: params,
      queryParamsHandling: 'merge',
    });
  }

  ngAfterViewInit() {
    // Initialize keyboard and mouse event listeners
    this.listenKeyboardEvent();
    this.listenMouseEvent();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private listenKeyboardEvent() {
    this.renderer2.listen('document', 'keydown', (event: KeyboardEvent) => {
      if (event.key === 'Shift') {
        // Track Shift press - if it was released before, mark it as no longer released
        if (this.shiftWasReleased()) {
          this.shiftWasReleased.set(false);
          console.log('🔄 Shift pressed after release - anchor persistence maintained');
        }
        this.isShiftPressed.set(true);
      }
      if (event.key === 'Control') {
        this.isCtrlPressed.set(true);
      }
    });
    this.renderer2.listen('document', 'keyup', (event: KeyboardEvent) => {
      if (event.key === 'Shift') {
        this.isShiftPressed.set(false);
        this.shiftWasReleased.set(true);
        console.log('🔄 Shift released - anchor will persist for next Shift+Click');
      }
      if (event.key === 'Control') {
        this.isCtrlPressed.set(false);
      }
    });
  }

  private listenMouseEvent() {
    const knowledgeBaseElement = document.querySelector(
      'app-knowledge-base-v2'
    );
    this.renderer2.listen(
      knowledgeBaseElement,
      'mousedown',
      (event: MouseEvent) => {
        // Chỉ ngăn chặn sự kiện mặc định khi cần thiết
        // Không ngăn chặn sự kiện trên các phần tử input, select, button
        const target = event.target as HTMLElement;
        if (
          target.tagName !== 'INPUT' &&
          target.tagName !== 'SELECT' &&
          target.tagName !== 'BUTTON' &&
          !target.closest('input') &&
          !target.closest('select') &&
          !target.closest('button') &&
          !target.closest('app-select') &&
          !target.closest('mat-menu-item')
        ) {
          // Chỉ ngăn chặn sự kiện mặc định cho các phần tử không phải là input, select, button
          // event.preventDefault();
        }
      }
    );
    this.renderer2.listen(
      knowledgeBaseElement,
      'mouseup',
      (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        if (
          target.classList.contains('knowledge-base-wrapper') ||
          target.classList.contains('file-folder-list-wrapper')
        ) {
          this.clearAllSelectedFolderAndFile();
        }
      }
    );
  }

  // Bulk operations
  deleteAll() {
    let title = '';
    let content = '';
    if (this.fileSelection.selected.length > 0) {
      title = 'Delete all these files';
      content = 'Are you sure delete all these files?';
    }
    if (this.folderSelection.selected.length > 0) {
      title = 'Delete all these folders';
      content = 'Are you sure delete all these folders?';
    }
    if (
      this.fileSelection.selected.length > 0 &&
      this.folderSelection.selected.length > 0
    ) {
      title = 'Delete all these files and folders';
      content = 'Are you sure delete all these files and folders?';
    }

    if (title) {
      this.dialogService
        .open(ConfirmDialogComponent, {
          data: {
            title,
            content,
            isDelete: true,
          },
        })
        .afterClosed()
        .subscribe((confirmed: unknown) => {
          if (confirmed) {
            this.deleteAllFile();
            this.deleteAllFolder();
          }
        });
    }
  }

  deleteAllFile() {
    if (this.fileSelection.selected.length > 0) {
      const body = {
        list_id: this.fileSelection.selected.map((item) => item.id),
      };
      this.fileSelection.clear();
      this.KnowledgeBaseService.deleteListFAQ(body).subscribe({
        next: (res) => {
          this.showSnackBar('Delete successfully', 'success');
          this.onRefreshRequested();
        },
        error: (error) => {
          this.showSnackBar('Error deleting files', 'error');
        },
      });
    }
  }

  deleteAllFolder() {
    if (this.folderSelection.selected.length > 0) {
      const body = {
        folder_ids: this.folderSelection.selected.map(
          (item) => item.id
        ) as number[],
      };
      const updatedFolderList = this.folderList().map((file) => {
        if (
          this.folderSelection.selected.map((item) => item.id).includes(file.id)
        ) {
          return { ...file, isDeleting: true };
        }
        return file;
      });
      this.folderList.set(updatedFolderList);
      this.folderSelection.clear();
      this.fileFolderService.deleteAllFolder(body).subscribe({
        next: (res) => {
          this.showSnackBar('Delete successfully', 'success');
          this.onRefreshRequested();
          this.clearAllSelectedFolderAndFile();
        },
        error: (error) => {
          this.showSnackBar(error.error.detail, 'error');
        },
      });
    }
  }
  // View switching
  toggleView(style: 'grid' | 'list') {
    this.showStyle.set(style);
    this.cdr.detectChanges();
  }

  onFolderDoubleClick(folder: IFolder) {
    this.toFolder(folder);
  }

  // Navigation methods
  toFolder(folder?: IFolder, mode?: string) {
    if (folder && folder.id) {
      this.parentId.set(folder.id);
      this.searchModel.id = folder.id;

      // Update breadcrumbs
      if (mode !== 'auto') {
        this.updateBreadcrumb(folder);
      }

      // Navigate to folder route
      this.router.navigate(['/knowledge-base', folder.id]);
    } else {
      // Navigate to root
      this.parentId.set(0);
      this.searchModel.id = 0;
      this.updateBreadcrumb(null);
      this.router.navigate(['/knowledge-base']);
    }

    // Load folder contents
    this.viewFolder({ ...this.searchModel, id: this.getParentId() });
  }

  navigateToHome() {
    this.toFolder();
  }

  navigateToFolder(folder: IFolder) {
    this.toFolder(folder);
  }

  onFileDoubleClick(file: IFile) {
    this.viewContentFile(file);
  }

  onFolderContextMenu(event: { folder: IFolder; event: MouseEvent }) {
    // Store selected folder for context menu actions
    this.selectedFolder.set(event.folder);
  }

  onFileContextMenu(event: { file: IFile; event: MouseEvent }) {
    // Store selected file for context menu actions
    this.selectedFile.set(event.file);
  }

  onSortChanged(event: { order: string; orderBy: string }) {
    this.searchModel.order = event.order as 'ASC' | 'DESC';
    this.searchModel.sort_by = event.orderBy;
    this.applyFilter();
  }

  // Event handlers from child components for specific actions
  onFolderRename(folder: IFolder) {
    this.selectFolder(folder);
    this.renameFolder(folder);
  }

  onFolderDelete(folder: IFolder) {
    this.selectFolder(folder);
    this.deleteFolder(folder);
  }

  onFileInfo(file: IFile) {
    this.selectFile(file);
    this.viewFileInfo(file);
  }

  onFileRename(file: IFile) {
    this.selectFile(file);
    this.renameFile(file);
  }

  onFileMove(file: IFile) {
    this.selectFile(file);
    this.moveFile(file);
  }

  onFileDelete(file: IFile) {
    this.selectFile(file);
    this.deleteFile(file);
  }

  onRefreshRequested() {
    // Refresh the current folder view
    this.viewFolder({ ...this.searchModel, id: this.getParentId() });
  }

  // Navigation methods
  goToHome() {
    this.router.navigate(['/knowledge-base']);
  }

  goToBreadcrumbFolder(folder: IFolder) {
    // Update breadcrumb list to remove items after the clicked folder
    const index = this.breadcrumbList().findIndex(
      (f: IFolder) => f.id === folder.id
    );
    if (index >= 0) {
      this.breadcrumbList.set(this.breadcrumbList().slice(0, index + 1));
    }
    this.router.navigate(['/knowledge-base', folder.id]);
  }

  toBreadcrumbItem(folder: IFolder | undefined) {
    if (folder) {
      // Find the index of the clicked breadcrumb
      const index = this.breadcrumbs().findIndex(
        (b: IFolder) => b.id === folder.id
      );
      if (index >= 0) {
        // Remove all breadcrumbs after the clicked one
        this.breadcrumbs.set(this.breadcrumbs().slice(0, index + 1));
      }
      this.toFolder(folder);
    } else {
      // Go to root (Knowledge Base)
      this.breadcrumbs.set([]);
      this.toFolder();
    }
  }

  // Filter and search methods
  onSearchChange(value: string, updateParam: boolean = true) {
    this.searchModel.name = value.trim();
    this.searchingMode.set(value.trim().length > 0);
    this.searchModel.page = 1;
    this.hasMoreData.set(true);
    if(updateParam) {
      this.updateAllFilterParams();
    }
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if (this.searchingMode()) {
        this.searchFeature();
      } else {
        this.viewFolder({ ...this.searchModel, id: this.getParentId() });
      }
    }, 300);
  }

  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.searchModel.name = input.value;
    this.searchingMode.set(input.value.trim().length > 0);
    this.searchModel.page = 1;
    this.hasMoreData.set(true);

    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    this.searchTimeout = setTimeout(() => {
      if (this.searchingMode()) {
        this.searchFeature();
      } else {
        this.viewFolder({ ...this.searchModel, id: this.getParentId() });
      }
    }, 300);
  }

  applyFilter(updateParam: boolean = true) {
    this.searchModel.page = 1;
    this.hasMoreData.set(true);
    this.searchingMode.set(
      !!(
        this.searchModel.name?.trim() ||
        this.searchModel.file_type?.trim() ||
        this.searchModel.file_status?.trim()
      )
    );
    if (updateParam) {
      this.updateAllFilterParams();
    }
    if (this.searchingMode()) {
      this.searchFeature();
    } else {
      this.viewFolder({ ...this.searchModel, id: this.getParentId() });
    }
  }

  clearSearch() {
    this.searchModel.name = '';
    this.searchingMode.set(false);
    this.applyFilter();
  }

  onFileTypeChange(value: any) {
    this.searchModel.file_type = value || '';
    this.applyFilter();
  }

  onFileStatusChange(value: any) {
    this.searchModel.file_status = value || '';
    this.applyFilter();
  }

  goToFileLocation(file: any) {
    if (file.folder_id) {
      this.KnowledgeBaseService.getFolderById(file.folder_id).subscribe(
        (res) => {
          this.toFolder(res, undefined);
        }
      );
    } else {
      this.toFolder(undefined);
    }
  }

  goToFolderLocation(folder: IFolder) {
    if (folder.parent_id) {
      this.KnowledgeBaseService.getFolderById(folder.parent_id).subscribe(
        (res) => {
          this.toFolder(res, undefined);
        }
      );
    } else {
      this.toFolder(undefined);
    }
  }

  private updateBreadcrumb(folder: IFolder | null): void {
    if (folder) {
      const currentBreadcrumbs = this.breadcrumbs();
      const indexBreadcrumbDup: number = currentBreadcrumbs.findIndex(
        (v: IFolder) => v.id === folder.id
      );
      if (indexBreadcrumbDup >= 0) {
        this.breadcrumbs.set(
          currentBreadcrumbs.slice(0, indexBreadcrumbDup + 1)
        );
      } else {
        this.breadcrumbs.set([...currentBreadcrumbs, folder]);
      }
    } else {
      this.breadcrumbs.set([]);
    }
    this.cdr.detectChanges();
  }

  // Data loading methods
  private getCollections() {
    this.collectionsService.getListCollection().subscribe((res) => {
      if (res) {
        this.collectionData.set(res);
        this.collectionOfFile.set(this.collectionData()[0].id);
      }
    });
  }

  private getListUserInAi() {
    this.settingsService.getUsersInAI().subscribe({
      next: (res) => {
        if (res) {
          this.listUserInAI.set(res);
          this.listUserInAIAllowed.set(res);
        }
      },
    });
  }

  private listenFileUpdateSocket() {
    this.subscription.add(
      this.socketService.listenEventCs('message').subscribe({
        next: (res: any) => {
          if (res) {
            if (res.type === 'FILES_UPDATE')
              this.viewFolder({ ...this.searchModel, id: this.getParentId() });
            if (res.type === 'FILES_DELETE')
              this.viewFolder({ ...this.searchModel, id: this.getParentId() });
            if (res.type === 'FILES_FAILED')
              this.showSnackBar(res.content, 'error');
          }
        },
      })
    );
  }

  private viewFolder(searchModel: ISearchModel) {
    this.searchingMode.set(!!searchModel.name);
    this.isLoadingMore.set(true);

    // Clear selections when loading new folder data
    this.clearAllSelectedFolderAndFile();

    // Ensure parentId is set correctly in searchModel
    const currentParentId = this.getParentId();
    if (
      currentParentId !== null &&
      (!searchModel.id || searchModel.id !== currentParentId)
    ) {
      searchModel = { ...searchModel, id: currentParentId };
    }

    // Convert ISearchModel to IFolderFilter for fileFolderService
    const folderFilter = this.convertSearchModelToFolderFilter(searchModel);

    this.fileFolderService.viewFolder(folderFilter).subscribe({
      next: (res) => {
        this.isLoadingMore.set(false);
        const data = res;

        if (data.pagination) {
          this.hasMoreData.set(data.pagination.has_more);
        } else {
          this.hasMoreData.set(false);
        }

        // Update folder list
        if (data && data?.folders) {
          this.folderList.set(
            data.folders.map((folder: IFolder) => ({ ...folder }))
          );
        } else {
          this.folderList.set([]);
        }

        // Update file list
        if (data && data?.files) {
          this.fileList.set(data.files.map((file: any) => ({ ...file })));
        } else {
          this.fileList.set([]);
        }

        // Update permissions
        if (data && data?.permissions) {
          this.permissions = data?.permissions;
          this.parent_folder_permissions = data?.permissions;
        }
        this.updateCombinedList();
        this.cdr.detectChanges();
        if (!this.isNavigatingToFolder()) {
          setTimeout(() => {
            this.refreshVirtualScrollViewports();
            this.cdr.detectChanges();
          }, 300);
        }
      },
      error: (error) => {
        this.isLoadingMore.set(false);
        console.error('Error loading folder data:', error);
        this.cdr.detectChanges();
      },
    });
  }

  // Load more functionality
  loadMore() {
    if (this.hasMoreData() && !this.isLoadingMore()) {
      this.searchModel.page = (this.searchModel.page || 1) + 1;
      if (this.searchingMode()) {
        this.searchFeature(true);
      } else {
        this.viewFolderLoadMore();
      }
    }
  }

  private viewFolderLoadMore() {
    this.isLoadingMore.set(true);
    const folderFilter = this.convertSearchModelToFolderFilter(
      this.searchModel
    );
    this.fileFolderService.viewFolder(folderFilter).subscribe({
      next: (res) => {
        this.isLoadingMore.set(false);
        const data = res;

        if (data.pagination) {
          this.hasMoreData.set(data.pagination.has_more);
        } else {
          this.hasMoreData.set(false);
        }

        // Append new folders
        if (data && data?.folders) {
          const newFolders = data.folders.map((folder: IFolder) => ({
            ...folder,
          }));
          this.folderList.set([...this.folderList(), ...newFolders]);
        }

        // Append new files
        if (data && data?.files) {
          const newFiles = data.files.map((file: any) => ({ ...file }));
          this.fileList.set([...this.fileList(), ...newFiles]);
        }

        // Update permissions if available
        if (data && data?.permissions) {
          this.permissions = data?.permissions;
          this.parent_folder_permissions = data?.permissions;
        }

        // Update combined list
        this.updateCombinedList();
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.isLoadingMore.set(false);
        console.error('Error loading more data:', error);
      },
    });
  }

  // Search functionality
  searchFeature(loadMore: boolean = false) {
    if (!loadMore) {
      this.searchingMode.set(
        !!(
          this.searchModel.name?.trim() ||
          this.searchModel.file_type?.trim() ||
          this.searchModel.file_status?.trim()
        )
      );
      this.folderList.set([]);
      this.fileList.set([]);
      this.combinedList.set([]);
    } else {
      this.isLoadingMore.set(true);
    }

    this.KnowledgeBaseService.searchingAllFileFolder(
      this.searchModel
    ).subscribe({
      next: (res) => {
        this.isLoadingMore.set(false);
        const data = res;

        if (data.pagination) {
          this.hasMoreData.set(data.pagination.has_more);
        } else {
          this.hasMoreData.set(false);
        }

        if (data && data?.folders) {
          const newFolders = data.folders.map((folder: IFolder) => ({
            ...folder,
          }));
          this.folderList.set(
            loadMore ? [...this.folderList(), ...newFolders] : newFolders
          );
        } else if (!loadMore) {
          this.folderList.set([]);
        }

        if (data && data?.files) {
          const newFiles = data.files.map((file: any) => ({ ...file }));
          this.fileList.set(
            loadMore ? [...this.fileList(), ...newFiles] : newFiles
          );
        } else if (!loadMore) {
          this.fileList.set([]);
        }

        if (data && data?.permissions) {
          this.permissions = data?.permissions;
          this.parent_folder_permissions = data?.permissions;
        }

        this.updateCombinedList();
        this.cdr.detectChanges();

        if (!this.isNavigatingToFolder()) {
          setTimeout(() => {
            this.refreshVirtualScrollViewports();
            this.cdr.detectChanges();
          }, 300);
        }
      },
      error: (error) => {
        this.isLoadingMore.set(false);
        console.error('Error searching:', error);
      },
    });
  }

  // Utility methods
  trackByFolderId(index: number, folder: IFolder): number {
    return folder.id || 0;
  }

  trackByFileId(index: number, file: IFile): number {
    return file.id || 0;
  }

  // Context menu actions
  renameFolderAction() {
    const folder = this.selectedFolder();
    if (folder) {
      this.renameFolder(folder);
    }
  }

  deleteFolderAction() {
    const folder = this.selectedFolder();
    if (folder) {
      this.deleteFolder(folder);
    }
  }

  viewFileInfoAction() {
    const file = this.selectedFile();
    if (file) {
      this.viewFileInfo(file);
    }
  }

  renameFileAction() {
    const file = this.selectedFile();
    if (file) {
      this.renameFile(file);
    }
  }

  moveFileAction() {
    const file = this.selectedFile();
    if (file) {
      this.moveFile(file);
    }
  }

  deleteFileAction() {
    const file = this.selectedFile();
    if (file) {
      this.deleteFile(file);
    }
  }

  // File and folder operations
  private renameFolder(folder: IFolder) {
    this.openCreateFolderDialog(folder);
  }

  deleteFolder(folder: IFolder) {
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this folder',
          content: 'Are you sure delete this folder?',
          isDelete: true,
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (value && folder && folder.id) {
          this.clearAllSelectedFolderAndFile();
          this.confirmDeleteFolder(folder.id);
        }
      });
  }

  confirmDeleteFolder(id: number) {
    const currentFolderList = this.folderList();
    const index = currentFolderList.findIndex(
      (item: IFolder) => item.id === id
    );
    if (index !== -1) {
      const updatedList = [...currentFolderList];
      updatedList[index] = {
        ...updatedList[index],
        isDeleting: true,
      };
      this.folderList.set(updatedList);
    }
    this.fileFolderService.deleteFolder(id).subscribe(
      (res) => {
        this.showSnackBar('Delete successfully', 'success');
        this.viewFolder({ ...this.searchModel, id: this.getParentId() });
      },
      (error) => {
        if (index !== -1) {
          const currentList = this.folderList();
          const updatedList = [...currentList];
          updatedList[index] = {
            ...updatedList[index],
            isDeleting: false,
          };
          this.folderList.set(updatedList);
        }
        this.showSnackBar('Delete failed', 'error');
      }
    );
  }

  viewFileInfo(file: IFile) {
    this.dialogService
      .open(AddOrEditFileComponent, {
        data: { file: file, isView: true },
        width: '50vw',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe(() =>
        this.viewFolder({ ...this.searchModel, id: this.getParentId() })
      );
  }

  viewContentFile(file: IFile) {
    this.KnowledgeBaseService.getContentFile(file.id!).subscribe({
      next: (res) => {
        this.fileContent.set(res.data || '');
        this.dialogService
          .open(ViewOrEditContentFileComponent, {
            data: { file: file, content: res.data },
            width: '80vw',
            height: '80vh',
          })
          .afterClosed()
          .subscribe((result) => {
            if (result) {
              // Hiện thông báo thành công
              this.showSnackBar('File content saved successfully', 'success');
              // Tải lại dữ liệu
              this.onRefreshRequested();
            }
          });
      },
      error: (error) => {
        console.error('Error loading file content:', error);
        this.fileContent.set('Error loading file content');
      },
    });
  }

  // Move file functionality
  moveFile(file: IFile) {
    this.dialogService
      .open(MoveFileComponent, {
        data: {
          file: file,
          parent_id: this.getParentId()
        },
        width: '40vw',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((result) => {
          this.clearAllSelectedFolderAndFile();
          this.viewFolder({ ...this.searchModel, id: this.getParentId() });
      });
  }









  private renameFile(file: IFile) {
    this.openRenameFileDialog(file);
  }

  openRenameFileDialog(file: IFile) {
    this.fileRename = {
      file_id: file.id,
      new_name: file.name,
    };
    this.dialogService
      .open(AddOrEditFileComponent, {
        data: {
          file: file,
        },
        width: '25vw',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((result) => {
        if (result && typeof result === 'number') {
          this.viewFolder({ ...this.searchModel, id: this.getParentId() });
          /*const selectedFile = this.selectedFile();
        const currentFileList = this.fileList();
        const fileIndex = currentFileList.findIndex(
          (file: IFile) => file.id === this.fileRename.file_id
        );
        if (fileIndex !== -1) {
          const updatedFileList = [...currentFileList];
          updatedFileList[fileIndex].name = this.fileRename.new_name;
          this.fileList.set(updatedFileList);
        }
        if (
          selectedFile &&
          selectedFile.id === this.fileRename.file_id
        ) {
          this.selectedFile.set({
            ...selectedFile,
            name: this.fileRename.new_name
          });
        }*/
        }
      });
  }

  deleteFile(file: IFile) {
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Delete this file',
          content: 'Are you sure delete this file?',
          isDelete: true,
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          this.confirmDeleteFile(file.id);
          this.clearAllSelectedFolderAndFile();
        }
      });
  }

  confirmDeleteFile(id: number | undefined) {
    if (!id) return;
    const currentFileList = this.fileList();
    const index = currentFileList.findIndex((item: IFile) => item.id === id);
    if (index === -1) return;

    const updatedList = [...currentFileList];
    updatedList[index] = {
      ...updatedList[index],
      isDeleting: true,
    };
    this.fileList.set(updatedList);

    this.KnowledgeBaseService.deleteFAQ(id).subscribe(
      (res) => {
        this.showSnackBar('Delete successfully', 'success');
        this.onRefreshRequested();
      },
      (error) => {
        const currentList = this.fileList();
        const revertedList = [...currentList];
        revertedList[index] = {
          ...revertedList[index],
          isDeleting: false,
        };
        this.fileList.set(revertedList);
      }
    );
  }

  // File upload and import
  showUploadDialog() {
    this.dialogService
      .open(UploadFileComponent, {
        data: {
          parent_id: this.parentId(),
        },
        width: '40vw',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((value) => {
        if (!!value) {
          this.viewFolder({ ...this.searchModel, id: this.getParentId() });
        }
      });
  }

  showImportUrlDialog() {
    if (this.collectionList().length > 0) {
      this.collectionOfFile.set(this.collectionList()[0].id);
    }
    /*this.showDialog(this.importUrlDialog(), {
      data: {},
      width: '40vw',
      minWidth: '350px',
    });*/
    this.dialogService
      .open(ImportUrlComponent, {
        data: {
          rootURL: '',
          collectionList: this.collectionList(),
          collectionOfFile: this.collectionOfFile(),
          modeGetUrl: this.modeGetUrl(),
          parentId: this.getParentId(),
        },
        width: '40dvw',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((value) => {
        if (!value) {
          this.viewFolder({ ...this.searchModel, id: this.getParentId() });
        }
      });
  }

  // Folder creation
  showCreateFolderDialog() {
    this.openCreateFolderDialog();
  }

  openCreateFolderDialog(folder?: IFolder) {
    let isCreate: boolean = true;
    if (folder) {
      isCreate = false;
    }
    this.dialogService
      .open(AddOrEditFolderComponent, {
        data: {
          isCreate: isCreate,
          parent_id: this.parentId(),
          folder: folder,
        },
        width: '25dvw',
        minWidth: '340px',
      })
      .afterClosed()
      .subscribe((value) => {
        if (value) {
          this.viewFolder({ ...this.searchModel, id: this.getParentId() });
        }
      });
  }

  // Helper methods
  private updateCombinedList(): void {
    // Preserve object references by modifying the original objects instead of creating new ones
    const folderItems = this.folderList().map((folder: IFolder) => {
      // Only set isFolder if it's not already set to avoid unnecessary object mutations
      if (folder.isFolder !== true) {
        folder.isFolder = true;
      }
      return folder;
    });
    const fileItems = this.fileList().map((file: any) => {
      // Only set isFolder if it's not already set to avoid unnecessary object mutations
      if (file.isFolder !== false) {
        file.isFolder = false;
      }
      return file;
    });
    this.combinedList.set([...folderItems, ...fileItems]);
  }

  public isFolder(item: { isFolder?: boolean } | any): boolean {
    return item && item.isFolder === true;
  }

  public isFile(item: { isFolder?: boolean } | any): boolean {
    return item && item.isFolder === false;
  }

  public trackByItemId(
    index: number,
    item: { isFolder?: boolean; id?: number } | any
  ): string | number {
    if (!item) return index;
    return item.isFolder ? `folder_${item.id}` : `file_${item.id}`;
  }

  // Helper method to safely get parentId
  private getParentId(): number {
    return this.parentId() || 0;
  }

  // Helper method to convert ISearchModel to IFolderFilter
  private convertSearchModelToFolderFilter(
    searchModel: ISearchModel
  ): IFolderFilter {
    return {
      id: searchModel.id || null,
      name: searchModel.name || '',
      file_type: searchModel.file_type || null,
      file_status: searchModel.file_status || null,
      sort_by: searchModel.sort_by || 'updated_at',
      order: searchModel.order || 'DESC',
      page: searchModel.page || 1,
      limit: searchModel.per_page || 50,
    };
  }

  private refreshVirtualScrollViewports() {
    // Refresh virtual scroll viewports
    const viewports = this.virtualScrollViewports();
    viewports.forEach((viewport: CdkVirtualScrollViewport) => {
      viewport.checkViewportSize();
    });
  }

  // Custom dialog methods for DxDialog
  showDxDialog(template: any, config: any) {
    return this.dialogService.open(template, config);
  }

  closeDxDialog() {
    this.dialogService.closeAll();
  }

  // Selection tracking - Updated to receive data from child components
  selectedFolders = signal<IFolder[]>([]);
  selectedFiles = signal<IFile[]>([]);

  selectedItemsCount = computed(() => {
    return this.selectedFolders().length + this.selectedFiles().length;
  });

  showSelectionInfo = computed(() => {
    return this.selectedItemsCount() > 0;
  });

  selectionText = computed(() => {
    const folderCount = this.selectedFolders().length;
    const fileCount = this.selectedFiles().length;
    const totalCount = folderCount + fileCount;

    if (totalCount === 0) return '';

    const parts: string[] = [];
    if (folderCount > 0) {
      parts.push(`${folderCount} folder${folderCount > 1 ? 's' : ''}`);
    }
    if (fileCount > 0) {
      parts.push(`${fileCount} file${fileCount > 1 ? 's' : ''}`);
    }

    return `${totalCount} selected (${parts.join(', ')})`;
  });

  // Event handlers from child components
  onFolderSelected(data: { folder: IFolder; event?: MouseEvent }) {
    this.selectFolder(data.folder, data.event);
  }

  onFileSelected(data: { file: IFile; event?: MouseEvent }) {
    this.selectFile(data.file, data.event);
  }

  // Updated selection methods to work with signals
  viewFilter = signal(false);
  openMenuImport = () => {
    this.openCreateMenu.set(true);
  };
  closeMenuExport = () => {
    this.openCreateMenu.set(false);
  }
  private selectFolder(folder: IFolder, event?: MouseEvent) {
    const isShift = this.isShiftPressed();
    const isCtrl = this.isCtrlPressed();

    this.logSelectionState(`Before folder selection: ${folder.name}`);

    if (isShift && (this.rangeAnchor() || this.lastSelectedItem())) {
      this.handleRangeSelection(folder, 'folder');
    } else if (isCtrl) {
      this.handleMultiSelection(folder, 'folder');
    } else {
      // Non-Shift, non-Ctrl click - this resets the anchor point
      this.handleSingleSelection(folder, 'folder');
      console.log('🔄 Anchor reset due to non-Shift click');
    }

    this.selectedFolders.set([...this.folderSelection.selected]);
    this.logSelectionState(`After folder selection: ${folder.name}`);

    // Force change detection in child components
    this.cdr.detectChanges();

    // Trigger change detection after a short delay to ensure UI updates
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 0);
  }

  private selectFile(file: IFile, event?: MouseEvent) {
    const isShift = this.isShiftPressed();
    const isCtrl = this.isCtrlPressed();

    this.logSelectionState(`Before file selection: ${file.name}`);

    if (isShift && (this.rangeAnchor() || this.lastSelectedItem())) {
      this.handleRangeSelection(file, 'file');
    } else if (isCtrl) {
      this.handleMultiSelection(file, 'file');
    } else {
      // Non-Shift, non-Ctrl click - this resets the anchor point
      this.handleSingleSelection(file, 'file');
      console.log('🔄 Anchor reset due to non-Shift click');
    }

    this.selectedFiles.set([...this.fileSelection.selected]);
    this.logSelectionState(`After file selection: ${file.name}`);

    // Force change detection in child components
    this.cdr.detectChanges();

    // Trigger change detection after a short delay to ensure UI updates
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 0);
  }

  // Selection mode handlers
  private handleSingleSelection(item: IFolder | IFile, type: 'folder' | 'file') {
    console.log('🎯 Single selection:', { item: item.name, type });

    // Clear all selections first
    this.folderSelection.clear();
    this.fileSelection.clear();

    // Select the clicked item
    if (type === 'folder') {
      this.folderSelection.select(item as IFolder);
    } else {
      this.fileSelection.select(item as IFile);
    }

    // Update both last selected item and range anchor for new single selection
    const index = this.getItemIndex(item, type);
    this.lastSelectedItem.set({ item, type, index });
    this.rangeAnchor.set({ item, type, index });

    console.log('🎯 New anchor set for single selection:', { item: item.name, type, index });
  }

  private handleMultiSelection(item: IFolder | IFile, type: 'folder' | 'file') {
    console.log('🎯 Multi selection (Ctrl+Click):', { item: item.name, type });

    // Toggle selection of the clicked item
    const wasSelected = type === 'folder'
      ? this.folderSelection.isSelected(item as IFolder)
      : this.fileSelection.isSelected(item as IFile);

    if (type === 'folder') {
      this.folderSelection.toggle(item as IFolder);
    } else {
      this.fileSelection.toggle(item as IFile);
    }

    // Update last selected item and set as new range anchor for Ctrl+Click
    const index = this.getItemIndex(item, type);
    this.lastSelectedItem.set({ item, type, index });
    this.rangeAnchor.set({ item, type, index });

    console.log('🎯 Ctrl+Click new anchor set:', {
      item: item.name,
      type,
      index,
      wasSelected,
      nowSelected: !wasSelected
    });
  }

  private handleRangeSelection(item: IFolder | IFile, type: 'folder' | 'file') {
    console.log('🎯 Range selection (Shift+Click):', { item: item.name, type });

    // Get the anchor point - use rangeAnchor if available, otherwise lastSelectedItem
    let anchor = this.rangeAnchor();
    if (!anchor) {
      anchor = this.lastSelectedItem();
      if (!anchor) {
        console.log('❌ No anchor available, falling back to single selection');
        this.handleSingleSelection(item, type);
        return;
      }
      // Set the anchor for the first time in this range selection sequence
      this.rangeAnchor.set(anchor);
      console.log('🎯 Setting initial range anchor:', { item: anchor.item.name, type: anchor.type, index: anchor.index });
    }

    const currentIndex = this.getItemIndex(item, type);
    const anchorIndex = anchor.index;

    // Handle case where item is not found
    if (currentIndex === -1 || anchorIndex === -1) {
      console.log('❌ Item not found, falling back to single selection');
      this.handleSingleSelection(item, type);
      return;
    }

    console.log('🎯 Range selection details:', {
      persistentAnchor: { item: anchor.item.name, type: anchor.type, index: anchorIndex },
      current: { item: item.name, type, index: currentIndex },
      isCtrlPressed: this.isCtrlPressed()
    });

    // Windows Explorer behavior: Ctrl+Shift adds range to existing selection
    if (!this.isCtrlPressed()) {
      // Clear current selections for normal Shift+Click
      this.folderSelection.clear();
      this.fileSelection.clear();
    }

    // Select range from persistent anchor to current item
    this.selectRange(anchorIndex, currentIndex);

    // Update last selected item but NEVER change the rangeAnchor during Shift+Click sequence
    this.lastSelectedItem.set({ item, type, index: currentIndex });

    console.log('🎯 Range selection completed, anchor PRESERVED:', {
      persistentAnchor: anchor.item.name,
      newLastSelected: item.name,
      anchorRemainedUnchanged: true
    });
  }

  private selectRange(startIndex: number, endIndex: number) {
    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);
    const folderCount = this.folderList().length;
    const selectedItems: Array<{type: string, id: number, name: string, index: number}> = [];

    console.log('🎯 Selecting range:', { minIndex, maxIndex, folderCount });

    for (let i = minIndex; i <= maxIndex; i++) {
      if (i < folderCount) {
        // Select folder at index i
        const folder = this.folderList()[i];
        if (folder && folder.id !== undefined) {
          this.folderSelection.select(folder);
          selectedItems.push({ type: 'folder', id: folder.id, name: folder.name, index: i });
        }
      } else {
        // Select file at index (i - folderCount)
        const fileIndex = i - folderCount;
        const file = this.fileList()[fileIndex];
        if (file && file.id !== undefined) {
          this.fileSelection.select(file);
          selectedItems.push({ type: 'file', id: file.id, name: file.name, index: i });
        }
      }
    }

    console.log('🎯 Range selected items:', selectedItems.map(item => `${item.name} (${item.type})`));
  }

  private getItemIndex(item: IFolder | IFile, type: 'folder' | 'file'): number {
    // For grid view, we need to calculate index based on visual order (folders first, then files)
    // For list view, items are already in the correct combined order

    if (type === 'folder') {
      const folderIndex = this.folderList().findIndex(folder => folder.id === item.id);
      return folderIndex === -1 ? -1 : folderIndex; // Folders come first in visual order
    } else {
      const fileIndex = this.fileList().findIndex(file => file.id === item.id);
      return fileIndex === -1 ? -1 : this.folderList().length + fileIndex; // Files come after folders
    }
  }

  private getCombinedList(): (IFolder | IFile)[] {
    // Use the same combined list that's displayed in the UI
    return this.combinedList();
  }

  // Debug method to log current selection state
  private logSelectionState(action: string) {
    const anchor = this.rangeAnchor();
    const lastSelected = this.lastSelectedItem();

    console.log(`🔍 Selection State (${action}):`, {
      anchor: anchor ? `${anchor.item.name} (${anchor.type}, index: ${anchor.index})` : 'none',
      lastSelected: lastSelected ? `${lastSelected.item.name} (${lastSelected.type}, index: ${lastSelected.index})` : 'none',
      shiftPressed: this.isShiftPressed(),
      ctrlPressed: this.isCtrlPressed(),
      shiftWasReleased: this.shiftWasReleased(),
      selectedFolders: this.selectedFolders().length,
      selectedFiles: this.selectedFiles().length
    });
  }

  clearAllSelectedFolderAndFile() {
    console.log('🧹 Clearing all selections and resetting anchors');

    // Recreate SelectionModel instances to trigger change detection in child components (OnPush)
    this.folderSelection = new SelectionModel<IFolder>(true, []);
    this.fileSelection = new SelectionModel<IFile>(true, []);

    // Reset selection signals
    this.selectedFolders.set([]);
    this.selectedFiles.set([]);
    this.lastSelectedItem.set(null);

    // Reset anchor points for Windows Explorer behavior
    this.rangeAnchor.set(null);
    this.shiftWasReleased.set(true);

    // Force change detection so UI updates promptly
    this.cdr.detectChanges();
  }

  getFolderById(folder_id: number) {
    return this.foldersCanMoveTo().find((folder) => folder.id === folder_id);
  }

  get isAllowAssign(): boolean {
    return Boolean(
      this.roleUserInAI &&
        (this.roleUserInAI === 'OWNER' || this.roleUserInAI === 'ADMIN')
    );
  }

  openAssignPermissionDialog() {
    if (this.folderSelection.selected.length == 0) return;
    const body = {
      folder_ids: this.folderSelection.selected
        .map((item) => item.id!)
        .filter((id): id is number => id !== undefined),
    };
    this.fileFolderService.getPermissionAssigned(body).subscribe({
      next: (res) => {
        if (res) {
          res.assign_permissions = res.assign_permissions.map((v: any) => {
            return {
              ...v,
              username: v?.user?.email,
              permissions: this.numberToBinaryComponents(v.permission),
            };
          });
          this.assignFolderForm.patchValue(res);
          if (res.assign_permissions.length > 0) {
            while (
              this.assignPermissionsFormArray.length <
              res.assign_permissions.length
            ) {
              this.assignPermissionsFormArray.push(
                this.fb.group({
                  user_id: [null, [Validators.required]],
                  username: [''],
                  folder_id: [null],
                  permissions: [[], [Validators.required]],
                })
              );
            }
            res.assign_permissions.forEach((val: any, index: number) => {
              (
                this.assignPermissionsFormArray.at(index) as FormGroup
              ).patchValue(val);
            });
            this.listUserInAIAllowed.set(
              this.listUserInAI().filter((user: any) =>
                this.assignPermissionsFormArray
                  .getRawValue()
                  .some((v: any) => v.user_id != user.id)
              )
            );
          }
        }
      },
    });
    this.userSelection.clear();
    this.assignFolderForm.patchValue({
      folder_ids: this.folderSelection.selected.map((v) => v.id),
    });
    while (this.assignPermissionsFormArray.length !== 0) {
      this.assignPermissionsFormArray.removeAt(0);
    }
    this.showDxDialog(this.assignPermissionFolderDialog(), {
      data: {
        countFolder: this.folderSelection.selected.length,
      },
      width: '35vw',
    });
  }

  get assignPermissionsFormArray() {
    return this.assignFolderForm.get('assign_permissions') as FormArray;
  }

  getAssignPermissionsFormArrayValue(name?: string) {
    if (!name) {
      return this.assignPermissionsFormArray.value;
    } else {
      return this.assignPermissionsFormArray.controls.map(
        (v) => (v as any).controls[name].value
      )[0];
    }
  }

  getAssignPermissionsFormArrayValueIndex(name: string, index: number) {
    if (name) {
      return (this.assignPermissionsFormArray.at(index) as FormGroup).controls[
        name
      ].value;
    }
  }

  addAssignPermissions(event: any) {
    const user_id = typeof event === 'number' ? event : event?.value || null;
    if (!user_id || this.userSelection.isSelected(user_id)) return;
    this.userSelection.select(user_id);
    this.assignPermissionsFormArray.push(
      this.fb.group({
        user_id: [user_id, [Validators.required]],
        folder_id: [null],
        username: [
          user_id && this.listUserInAI()
            ? this.listUserInAI().find((v: any) => v.id === user_id)?.email ||
              ''
            : '',
        ],
        permissions: [[], [Validators.required]],
      })
    );
  }

  selectPermissions(permission: any, index: number) {
    // Ensure permission is an array for consistent processing
    let permissionsArray: number[] = [];
    if (Array.isArray(permission)) {
      permissionsArray = permission;
    } else if (permission !== null && permission !== undefined) {
      permissionsArray = [permission];
    }

    // Auto-include Read (1) when Write (2) or Delete (4) is selected
    if (permissionsArray.includes(2) || permissionsArray.includes(4)) {
      permissionsArray.push(1);
    }

    // Remove duplicate permissions
    permissionsArray = [...new Set(permissionsArray)];

    const oldValue = {
      ...this.assignPermissionsFormArray.at(index).value,
    };

    // Only patch if value actually changed to avoid infinite loops
    const currentPermissions = oldValue.permissions as number[];
    const isSameLength = currentPermissions.length === permissionsArray.length;
    const hasSameElements =
      isSameLength &&
      permissionsArray.every((p) => currentPermissions.includes(p));

    if (!isSameLength || !hasSameElements) {
      this.assignPermissionsFormArray.at(index).patchValue({
        ...oldValue,
        permissions: permissionsArray,
      });

      // Manually update form validity in case it doesn't reflect automatically
      (
        this.assignPermissionsFormArray.at(index) as FormGroup
      ).markAllAsTouched();
      this.assignFolderForm.updateValueAndValidity();
    }
  }

  savePermissionAssign() {
    if (this.assignFolderForm.invalid) return;
    this.isSavingPermission.set(true);
    const body = {
      ...this.assignFolderForm.value,
    };
    body.assign_permissions = body.assign_permissions.map((v: any) => {
      const biPermission = v.permissions.reduce(
        (a: number, b: number) => a | b,
        0
      );
      return {
        ...v,
        permissions: biPermission,
      };
    });
    this.fileFolderService.assignUserFolderPermissions(body).subscribe({
      next: (res) => {
        this.showSnackBar('Assign permissions successfully', 'success');
        this.viewFolder({ ...this.searchModel });
        this.isSavingPermission.set(false);
        this.userSelection.clear();
        this.folderSelection.clear();
        this.closeDxDialog();
      },
      error: (error) => {
        this.isSavingPermission.set(false);
        this.viewFolder({ ...this.searchModel });
        this.showSnackBar(error.error.detail, 'error');
        this.userSelection.clear();
        this.folderSelection.clear();
        this.closeDxDialog();
      },
    });
  }

  private numberToBinaryComponents(permission: number): number[] {
    const result: number[] = [];
    if (permission & 1) result.push(1);
    if (permission & 2) result.push(2);
    if (permission & 4) result.push(4);
    return result;
  }
}
