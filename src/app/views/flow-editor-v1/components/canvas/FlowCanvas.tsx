// @ts-nocheck
import { flowDev<PERSON>pi, variableLocated<PERSON><PERSON> } from "@flow-editor-v1/api"
import CsEdge from "@flow-editor-v1/components/edge/CsEdge"
import MiniMapToolBar from "@flow-editor-v1/components/flow/MiniMapToolbar"
import NodeContextMenu from "@flow-editor-v1/components/flow/NodeContextMenu"
import ApiNode from "@flow-editor-v1/components/nodes/api/ApiNode"
import BasicLLMChainNode from "@flow-editor-v1/components/nodes/basicLLMChain/basicLLMChainNode"
import CarouselNode from "@flow-editor-v1/components/nodes/carousel/CarouselNode"
import EQuickRepliesNode from "@flow-editor-v1/components/nodes/eQuickReplies/EQuickRepliesNode"
import EventApiNode from "@flow-editor-v1/components/nodes/eventApi/EventApiNode"
import EventIfElseNode from "@flow-editor-v1/components/nodes/eventIfElse/EventIfElseNode"
import EventTriggerNode from "@flow-editor-v1/components/nodes/eventTrigger/EventTriggerNode"
import FunctionNode from "@flow-editor-v1/components/nodes/function/FunctionNode"
import GenericMessageNode from "@flow-editor-v1/components/nodes/genericMessage/GenericMessageNode"
import IfElseNode from "@flow-editor-v1/components/nodes/ifElse/IfElseNode"
import QuestionNode from "@flow-editor-v1/components/nodes/question/QuestionNode"
import QuickRepliesNode from "@flow-editor-v1/components/nodes/quickReplies/QuickRepliesNode"
import SendMessageNode from "@flow-editor-v1/components/nodes/sendMessage/SendMessageNode"
import StartNode from "@flow-editor-v1/components/nodes/start/StartNode"
import SwitchFlowNode from "@flow-editor-v1/components/nodes/switch/SwitchFlow"
import TextNode from "@flow-editor-v1/components/nodes/text/TextNode"
import ToolNode from "@flow-editor-v1/components/nodes/tool/ToolNode"
import ToolV2Node from "@flow-editor-v1/components/nodes/toolV2/ToolV2Node"
import VariableNode from "@flow-editor-v1/components/nodes/variable/VariableNode"
import { MAX_ZOOM, MIN_ZOOM, SAVE_DEBOUNCE, STUDIO_STATUS } from "@flow-editor-v1/constant"
import { useDeviceSupport, useFlowInstance } from "@flow-editor-v1/hook"
import { startNodeEvent, startNodeIntent } from "@flow-editor-v1/init"
import {
  BuildFlowState,
  ChatFlowDev,
  FlowDebugState,
  FlowInstanceState,
  IFlowCanvasProps,
  NodeFlow,
  NodeFlowData,
  StudioState
} from "@flow-editor-v1/model"
import {
  useBuildFlowState,
  useFlowDebugState,
  useFlowInstanceState,
  useMenuState,
  useStudioState,
  useVariableLocatedState
} from "@flow-editor-v1/store"
import { getUniqueNodeId, LocalStorageKey } from "@flow-editor-v1/utils/flow"
import type { Edge } from "@reactflow/core/dist/esm/types/edges"
import { Connection } from "@reactflow/core/dist/esm/types/general"
import type { Node } from "@reactflow/core/dist/esm/types/nodes"
import _, { debounce } from "lodash"
import * as React from "react"
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react"
import ReactFlow, { Background, SelectionMode, useEdgesState, useNodesState } from "reactflow"
import useUndoable from "use-undoable"
import { FLOW_TRIGGER_TYPE } from "../../../../core/constants"
import LLMMessageNode from "../nodes/LLMMessage/LLMMessageNode"

const nodeTypes = {
  start: StartNode,
  tool: ToolNode,
  toolV2: ToolV2Node,
  api: ApiNode,
  eapi: EventApiNode,
  ifElse: IfElseNode,
  text: TextNode,
  question: QuestionNode,
  switchFlow: SwitchFlowNode,
  function: FunctionNode,
  variable: VariableNode,
  carousel: CarouselNode,
  eventTrigger: EventTriggerNode,
  eQuickReplies: EQuickRepliesNode,
  sendMessage: SendMessageNode,
  eIfElse: EventIfElseNode,
  basicLLMChain: BasicLLMChainNode,
  LLMMessage: LLMMessageNode,
  quickReplies: QuickRepliesNode,
  genericMessage: GenericMessageNode
}
const edgeTypes = {cs: CsEdge}

const FlowCanvas = forwardRef((props: IFlowCanvasProps, ref) => {
  const {
    flowId,
    listNodeCollapsed,
    socket,
    conversationId,
    messageApi,
    onOpenModalConfirm,
    onOk,
    onCancel
  } = props

  const [nodes, setNodes, onNodesChange] = useNodesState<NodeFlowData>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<any>([])

  useImperativeHandle(ref, () => ({
    async triggerSave() {
      await handleSave()
    }
  }))

  const {deleteNodes} = useFlowInstance()
  const {isCtrlKeyPressed} = useDeviceSupport()

  /**
   * State
   */
  const [
    panningMouseButton,
    setPanningMouseButton
  ] = useState<number[]>([1]);
  const [
    draggedNode,
    setDraggedNode
  ] = useState(null)
  const [
    firstElements,
    setFirstElements
  ] = useState({
    nodes: [],
    edges: [],
  })
  const [
    elements,
    setElements,
    {
      past,
      future,
      undo,
      canUndo,
      redo,
      canRedo,
      reset
    }
  ] = useUndoable({
    nodes: [],
    edges: [],
  }, {behavior: 'mergePast'})
  const [
    paramSourceNode,
    setParamSourceNode
  ] = useState<{ source: string, sourceHandle: string }>(null)
  const [
    selecting,
    setSelecting
  ] = useState<boolean>(false)

  /**
   * Store
   */
  const {
    flowInstance,
    setFlowInstance
  } = useFlowInstanceState<FlowInstanceState>((state) => state)
  const {
    isDirty,
    flow,
    dialogShowing,
    triggerSave,
    setFlow,
    setDirty,
    setNotDirty
  } = useBuildFlowState<BuildFlowState>((state: BuildFlowState) => state)
  const {
    status
  } = useStudioState<StudioState>((state) => state)
  const {
    debug,
    setDebug
  } = useFlowDebugState<FlowDebugState>(state => state)
  const {
    menu,
    setMenu
  } = useMenuState(state => state)
  const {variableLocations} = useVariableLocatedState(state => state)

  /**
   * Ref
   * */
  const isConnectingEdgeRef = useRef(false);
  const menuOpenRef = useRef(false)
  const isSocketConnectingRef = useRef(false)
  const intervalAutoSaveRef = useRef(null)
  const timeoutAutoSaveRef = useRef(null)


  /**
   * Func
   */
  const handleUndo = useCallback(
    () => {
      if (!canUndo || _.isEqual(past[past.length - 1], firstElements)) {
        reset()
        setNodes([...firstElements.nodes])
        setEdges([...firstElements.edges])
        setElements({...firstElements})
      } else {
        undo()
        const lastPast = past?.[past.length - 1]
        if (!lastPast || lastPast.nodes.length === 0 || lastPast.edges.length === 0) {
          reset()
          setElements({...firstElements})
        } else {
          setNodes([...lastPast.nodes])
          setEdges([...lastPast.edges])
        }
      }
    },
    [canUndo, past, undo, reset, setNodes, setEdges, setElements]
  )

  const handleRedo = useCallback(
    () => {
      if (!canRedo || _.isEqual(elements, firstElements)) {
        setNodes([...elements.nodes])
        setEdges([...elements.edges])
      } else {
        redo()
        const firstFuture = future?.[0]
        if (!firstFuture || firstFuture.nodes.length === 0 || firstFuture.edges.length === 0) {
          reset()
          setElements({...firstElements})
        } else {
          setNodes([...firstFuture.nodes])
          setEdges([...firstFuture.edges])
        }
      }
    },
    [canRedo, future, elements, firstElements, redo, reset, setNodes, setEdges, setElements, setFirstElements]
  )

  const handleSave = useCallback(
    async (flowDataStorage?: string | null, showToastr: boolean = false, useKeys: boolean = false) => {
      if (flow) {
        const body: ChatFlowDev = {
          ...flow,
          flow_data: flowDataStorage && useKeys
            ? flowDataStorage
            : JSON.stringify({
              nodes: [...flowInstance.getNodes()].map(node => ({
                ...node,
                selected: false,
                data: {...node.data, selected: false},
                style: {...node.style, opacity: 1},
              })),
              edges: [...flowInstance.getEdges()].map(edge => {
                if (edge.data?.isGoToBlock) {
                  return {
                    ...edge,
                    animated: false,
                    style: {
                      ...edge.style,
                      opacity: 0,
                    },
                  }
                } else {
                  return {
                    ...edge,
                    animated: false,
                    style: {
                      ...edge.style,
                      stroke: "white",
                      strokeWidth: 1,
                      strokeOpacity: 1,
                      opacity: 1,
                    },
                  }
                }
              }),
            }),
        }

        await Promise.all([
          flowDevApi.saveFlowDataDev(body),
          variableLocatedApi.saveVariableLocated(variableLocations),
        ])

        if (!flowDataStorage && showToastr) messageApi.success("Flow saved")

        if (useKeys) {
          localStorage.setItem(LocalStorageKey.flowDevData(body.id), body.flow_data)
          localStorage.setItem(LocalStorageKey.flowDevDataUpdatedAt(flow.id), new Date().toISOString())
        }

        setNotDirty()
      }
    },
    [flow, flowInstance, flowDevApi, variableLocatedApi, variableLocations, messageApi, setNotDirty]
  )

  const handleCopy = useCallback(
    (e: KeyboardEvent) => {
      if (!flowInstance || debug) return;

      const selectedContent = window.getSelection()?.toString();
      const selectedNodes = flowInstance.getNodes().filter((node) => node.selected && node.id !== 'start_0');
      if (selectedContent && !selectedNodes.length) {
        void navigator.clipboard.writeText(selectedContent)
        return;
      }

      e.preventDefault()

      const selectedNodeIds = selectedNodes.map((node) => node.id);
      const selectedEdges = flowInstance
        .getEdges()
        .filter((edge) => selectedNodeIds.includes(edge.source) && selectedNodeIds.includes(edge.target));
      if (!selectedEdges.length && !selectedNodes.length) return;

      const dataToCopy = JSON.stringify({
        nodes: selectedNodes,
        edges: selectedEdges,
        sourceFlowId: flowId,
      });

      navigator.clipboard.writeText(dataToCopy)
        .then(() => messageApi.success('Data copied to clipboard'))
        .catch((err) => messageApi.error('Failed to copy:', err));
    },
    [flowInstance, debug]
  );

  const handlePaste = useCallback(
    (e: KeyboardEvent) => {
      navigator.clipboard.readText()
        .then((data) => {
          if (!data || debug) return

          // if (intervalAutoSaveRef.current) {
          //   clearInterval(intervalAutoSaveRef.current)
          // }

          let dataCopied = null;
          try {
            dataCopied = JSON.parse(data);
          } catch (err) {
          }

          if (dataCopied && typeof dataCopied === 'object' && dataCopied.nodes?.length) {
            if (!flowInstance) return;

            resetStyle()

            e.preventDefault()

            const {nodes: nodesCopied, edges: edgesCopied, sourceFlowId} = dataCopied;
            const oldNodeIds = [...nodesCopied].map(node => node.id)

            let nodePositionPastedKey = '';
            for (const key in localStorage) {
              if (key.includes('nodePositionPasted') && key.includes(String(flowId))) {
                nodePositionPastedKey = key;
                break;
              }
            }

            if (nodePositionPastedKey) {
              const positionStored = JSON.parse(localStorage.getItem(nodePositionPastedKey))
              if (positionStored) {
                nodesCopied.forEach((node) => {
                  const storedPosition = positionStored[node.id];
                  if (storedPosition) {
                    node.position = storedPosition.position;
                    node.positionAbsolute = storedPosition.positionAbsolute;
                  }
                })
              }
            }

            const mousePosition = JSON.parse(localStorage.getItem(LocalStorageKey.mousePositionFlow(flowId)))
            if (mousePosition) {
              const {x: mouseX, y: mouseY} = flowInstance.screenToFlowPosition(mousePosition)
              let minY = Infinity, minX = Infinity;
              for (let node of nodesCopied) {
                if (node.position.y < minY) {
                  minY = node.position.y;
                }
                if (node.position.x < minX) {
                  minX = node.position.x;
                }
              }
              const selectionRectPosition = {x: minX, y: minY}

              const deltaX = mouseX - selectionRectPosition.x;
              const deltaY = mouseY - selectionRectPosition.y;

              nodesCopied.forEach((node) => {
                const {position: oldPosition} = node
                node.position = {x: oldPosition.x + deltaX, y: oldPosition.y + deltaY};
              })
              localStorage.removeItem(LocalStorageKey.mousePositionFlow(flowId))
            }

            const newNodes = []
            nodesCopied.forEach((node) => {
              const oldNodeId = node.id;
              const newNodeId = getUniqueNodeId(node.data, [...new Set([...flowInstance.getNodes(), ...newNodes])], flowId);

              if (!mousePosition) {
                const position = {
                  x: node.position.x + 100, y: node.position.y + 50,
                };
                const positionAbsolute = {
                  x: node.positionAbsolute.x + 100, y: node.positionAbsolute.y + 50,
                };
                node.position = position
                node.positionAbsolute = positionAbsolute
              }
              node.id = newNodeId
              node.data = {...node.data, id: newNodeId, selected: true, oldId: oldNodeId}

              newNodes.push(node)

              edgesCopied.forEach((edge) => {
                if (edge.id.includes(oldNodeId)) {
                  edge.id = edge.id.replace(oldNodeId, newNodeId);
                }
                if (edge.source === oldNodeId) {
                  edge.source = edge.source.replace(oldNodeId, newNodeId);
                  edge.sourceHandle = edge.sourceHandle.replace(oldNodeId, newNodeId);
                }
                if (edge.target === oldNodeId) {
                  edge.target = edge.target.replace(oldNodeId, newNodeId);
                  edge.targetHandle = edge.targetHandle.replace(oldNodeId, newNodeId);
                }
                edge.animated = true
                if (!edge.data?.isGoToBlock) {
                  edge.style = {
                    ...edge.style,
                    stroke: node.data.node_color,
                    strokeWidth: 2,
                    strokeOpacity: 1,
                    opacity: 1
                  }
                } else {
                  edge.style = {
                    ...edge.style,
                    opacity: 0
                  }
                }
              });
            });

            const positionNodes = nodesCopied.reduce((acc, item) => {
              acc[item.data.oldId] = {position: item.position, positionAbsolute: item.positionAbsolute};
              return acc;
            }, {});
            localStorage.setItem(LocalStorageKey.nodePositionPasted(oldNodeIds.join('-')), JSON.stringify(positionNodes));

            try {
              if (nodesCopied.length) {
                setNodes((nodes) =>
                  [...nodes.concat([...nodesCopied])]
                )
              }
              if (edgesCopied.length) {
                setEdges((edges) =>
                  [...edges.concat([...edgesCopied])]
                )
              }

              const isCrossFlowPaste = sourceFlowId && sourceFlowId !== flowId;

              if (isCrossFlowPaste) {
                messageApi.success(`Pasted ${nodesCopied.length} nodes from flow ${sourceFlowId}`);
              }
            } catch (err) {
              console.error(err);
            }
          }

          setDirty()
        })
        .catch((err) => console.error('Failed to read clipboard:', err));
    },
    [setNodes, setEdges, flowInstance, flowId, messageApi, debug]
  );

  const handleDelete = useCallback(
    () => {
      if (flowInstance && !debug) {
        const nodesDeleting = flowInstance.getNodes().filter((node) => node.selected && node.id !== 'start_0') || [];
        const nodeIds = nodesDeleting?.map(v => v.id)
        deleteNodes(nodeIds)
      }
    },
    [flowInstance, deleteNodes, debug]
  )

  /**
   * Key/Mouse bindings
   */
  const handleKeyDown = useCallback(
    async (event: React.KeyboardEvent) => {
      const modifiers = [
        isCtrlKeyPressed(event) ? "Control" : "",
        event.altKey ? "Alt" : "",
        event.shiftKey ? "Shift" : "",
      ]
        .filter(Boolean)
        .join("-")

      const keyCombination = `${modifiers}-${event.key.toLowerCase()}`.replace(/-$/, "")

      switch (keyCombination) {
        case "Control-z":
          if (debug) break
          handleUndo()
          break
        case "Control-Shift-z":
          if (debug) break
          handleRedo()
          break
        case "Control-s":
          if (debug) break
          await handleSave(null, true, true)
          break
        case "-Delete":
        case "-Backspace":
          if (debug) break
          handleDelete()
          break;
        default:
          break
      }
    },
    [handleUndo, handleRedo, handleSave, handleCopy, handlePaste, handleDelete, debug]
  )

  /**
   * Node
   * */
  const handleNodeClick = useCallback(
    () => {
      setMenu(null)
    },
    [setMenu]
  )

  /**
   * Drag & Drop
   */
  const handleNodeDragStart = useCallback(
    (_, node) => {
      setDraggedNode(node)
    },
    []
  )

  const handleNodeDragStop = useCallback(
    (event, node, nodes) => {
      if (draggedNode && _.isEqual(draggedNode, node)) {
        setDraggedNode(null)
        return
      }
      setDirty()
    },
    [draggedNode]
  )

  const handleSelectionDragStop = useCallback(
    (event: React.MouseEvent, _) => {
      setDirty()
    },
    []
  )

  /**
   * Connections / Edges
   */
  const handleConnectStart = useCallback(
    (event: React.MouseEvent, param) => {
      isSocketConnectingRef.current = true
      menuOpenRef.current = false
      const source = {source: param.nodeId, sourceHandle: param.handleId}
      setParamSourceNode(source)
    },
    []
  )

  const handleConnect = useCallback(
    (connection: Connection) => {
      isConnectingEdgeRef.current = true
      const newEdge = {
        ...connection,
        type: "cs",
        id: `${connection.source}-${connection.sourceHandle}-${connection.target}-${connection.targetHandle}`,
        data: {
          isGoToBlock: false
        }
      }

      const nodeAlreadyConnect = edges.find(v => v.source === paramSourceNode.source && v.sourceHandle === paramSourceNode.sourceHandle)
      setEdges((edges) => {
        if (nodeAlreadyConnect) {
          return edges.concat(newEdge).filter((edge) => edge.id !== nodeAlreadyConnect.id).map((edge) => edge)
        }
        return edges.concat(newEdge).map((edge) => edge)
      })

      setDirty()
    },
    []
  )

  const handleConnectEnd = useCallback(
    (event) => {
      event.preventDefault()
      if (!isConnectingEdgeRef.current) {
        setMenu({
          top: event.clientY > 134 ? event.clientY - 134 : 0,
          left: event.clientX > 320 ? (listNodeCollapsed ? event.clientX - 320 : event.clientX - 580) : 0
        });
      } else {
        isConnectingEdgeRef.current = false;
      }
      if (event.type == "contextmenu") {
        setParamSourceNode({source: "", sourceHandle: ""})
      }
    },
    [listNodeCollapsed]
  )

  /**
   * Edge and Nodes styled
   */
  const highlightNodeAndEdge = useCallback(
    (element: { nodes: Node[], edges: Edge[] }) => {
      if (debug) return;
      resetStyle()
      if (element.nodes.length === 1) {
        const selectionNode = element.nodes[0]
        setNodes((nds) =>
          nds.map((node) => {
            if (node.id !== selectionNode.id) {
              node.style = {
                ...node.style,
                opacity: node.id === 'start_0' ? 1 : 0.3
              }
            }
            return node;
          })
        );
        setEdges((eds) =>
          eds.map((edge) => {
            if (!(edge.target === selectionNode.id || edge.source === selectionNode.id)) {
              if (!edge.data?.isGoToBlock) {
                edge.style = {
                  ...edge.style,
                  opacity: 0.3
                }
              } else {
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            } else {
              if (!edge.data?.isGoToBlock) {
                edge.animated = true
                edge.style = {
                  ...edge.style,
                  stroke: selectionNode.data.node_color,
                  strokeWidth: 2,
                  strokeOpacity: 1,
                  opacity: 1
                }
              } else {
                edge.animated = false
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            }
            return edge
          })
        )
      }

      if (element.nodes.length > 1) {
        const selectedNodeIds = element.nodes.map((node) => node.id);
        setNodes((nds) =>
          nds.map((node) => {
            node.style = {
              ...node.style,
              opacity: selectedNodeIds.includes(node.id) ? 1 : (node.id === 'start_0' ? 1 : 0.3)
            }
            return node;
          })
        );
        const selectedEdgesIds = element.edges.filter(edge => selectedNodeIds.includes(edge.source) && selectedNodeIds.includes(edge.target)).map(edge => edge.id);
        setEdges((eds) =>
          eds.map((edge) => {
            if (!selectedEdgesIds.includes(edge.id)) {
              if (!edge.data?.isGoToBlock) {
                edge.style = {
                  ...edge.style,
                  opacity: 0.3
                }
              } else {
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            } else {
              if (!edge.data?.isGoToBlock) {
                edge.animated = true
                edge.style = {
                  ...edge.style,
                  stroke: '#0059DC',
                  strokeWidth: 2,
                  strokeOpacity: 1,
                  opacity: 1
                }
              } else {
                edge.animated = false
                edge.style = {
                  ...edge.style,
                  opacity: 0
                }
              }
            }
            return edge
          })
        )
      }
    },
    [debug]
  )

  const resetStyle = useCallback(
    () => {
      if (debug) return
      setNodes((nds) =>
        nds.map((node) => {
          node.style = {
            ...node.style,
            opacity: 1,
          }
          return node;
        })
      );
      setEdges((eds) =>
        eds.map((edge) => {
          if (!edge.data?.isGoToBlock) {
            edge.animated = false
            edge.style = {
              ...edge.style,
              stroke: 'white',
              strokeWidth: 1,
              strokeOpacity: 1,
              opacity: 1
            }
          } else {
            edge.animated = false
            edge.style = {
              ...edge.style,
              opacity: 0
            }
          }
          return edge
        })
      )
    },
    [debug, flowInstance]
  )

  /**
   * Drag and drop
   */
  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault()
      event.dataTransfer.dropEffect = "move"
    },
    []
  )

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault()
      if (
        !flow ||
        !status ||
        (flow && status && status === STUDIO_STATUS.LIVE)
      ) {
        void messageApi.warning("You cannot edit in production mode.")
        return
      }

      const nodeDataTransfer = event.dataTransfer.getData(
        "application/reactflow"
      )
      if (typeof nodeDataTransfer === "undefined" || !nodeDataTransfer) {
        return
      }
      const nodeData = JSON.parse(nodeDataTransfer)
      const position = flowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      })
      const newNodeId = getUniqueNodeId(
        nodeData,
        flowInstance.getNodes(),
        flowId
      )

      const newNode: NodeFlow = {
        id: newNodeId,
        position,
        type: nodeData.node_type,
        data: {
          ...nodeData,
          id: newNodeId,
        },
      }

      setNodes((nodes) =>
        nodes.concat(newNode).map((node) => {
          node.data = {
            ...node.data,
            selected: false,
          }
          return node
        })
      )

      setDirty()
    },
    [flowInstance, flow]
  )

  /**
   * Panel
   */
  const handlePanelClick = useCallback(
    (event: React.MouseEvent) => {
      if (debug) return
      menuOpenRef.current = !menuOpenRef.current;
      setMenu(null)

      resetStyle()

      localStorage.setItem(LocalStorageKey.mousePositionFlow(flowId), JSON.stringify({
        x: event.clientX,
        y: event.clientY
      }))
    },
    [debug]
  )

  /**
   * Drag-select
   */
  const handleSelectionChangeDebounce = (element: { nodes: Node[], edges: Edge[] }) => {
    if (debug) return;

    const resetNodesSelection = (selectedNodeIds: string[]) => {
      setNodes((nds) =>
        nds.map((node) => {
          node.data = {
            ...node.data,
            selected: selectedNodeIds.includes(node.id),
          };
          return node;
        })
      );
    };

    // Filter out start_0 node from selection
    const filteredNodes = element.nodes.filter(node => node.id !== 'start_0');
    const filteredElement = {
      ...element,
      nodes: filteredNodes
    };

    const nodeCount = filteredNodes.length;
    if (nodeCount === 0) {
      setSelecting(false);
      resetNodesSelection([]);
      return;
    }

    const selectedNodeIds = filteredNodes.map((node) => node.id);
    setSelecting(true);
    resetNodesSelection(selectedNodeIds);
    highlightNodeAndEdge(filteredElement);
  }

  const handleSelectionChange = useCallback(debounce(handleSelectionChangeDebounce, 250), [debug]);

  /**
   * Lifecycle
   */
  useEffect(
    () => {
      if (status && flow) {
        /*
        * TODO: Open a confirmation modal to choose between using flow data saved in LocalStorage or from the database when the LocalStorage data is more recent than the database.
        * */
        if (localStorage.getItem(LocalStorageKey.flowDevDataUpdatedAt(flowId)) && localStorage.getItem(LocalStorageKey.flowDevData(flowId)) && new Date(flow.updated_at) > new Date(localStorage.getItem(LocalStorageKey.flowDevDataUpdatedAt(flowId)))) {
          if (intervalAutoSaveRef.current) clearInterval(intervalAutoSaveRef.current)
          onOpenModalConfirm()
          return
        }
        setNodes(
          JSON.parse(flow.flow_data)
            ? JSON.parse(flow.flow_data).nodes
            : flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT ? [startNodeIntent] : flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT ? [startNodeEvent] : []
        )
        setEdges(
          JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data).edges : []
        )
        setFirstElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
        setElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
      }
    },
    [flowId, flow]
  )

  useEffect(() => {
    if (onOk) {
      const flow_data = JSON.parse(localStorage.getItem(LocalStorageKey.flowDevData(flowId)))
      setNodes(
        flow_data
          ? flow_data.nodes
          : flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT ? [startNodeIntent] : flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT ? [startNodeEvent] : []
      )
      setEdges(
        flow_data ? flow_data.edges : []
      )
      setFirstElements(flow_data ? flow_data : {nodes: [], edges: []})
      setElements(flow_data ? flow_data : {nodes: [], edges: []})
    }
    if (onCancel) {
      setNodes(
        JSON.parse(flow.flow_data)
          ? JSON.parse(flow.flow_data).nodes
          : flow.trigger_type === FLOW_TRIGGER_TYPE.INTENT ? [startNodeIntent] : flow.trigger_type === FLOW_TRIGGER_TYPE.EVENT ? [startNodeEvent] : []
      )
      setEdges(
        JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data).edges : []
      )
      setFirstElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
      setElements(JSON.parse(flow.flow_data) ? JSON.parse(flow.flow_data) : {nodes: [], edges: []})
    }
  }, [onOk, onCancel, flowId, flow]);

  useEffect(
    () => {
      const handleKeyDown = async (e: KeyboardEvent) => {
        const isCtrl = isCtrlKeyPressed(e)
        const isShift = e.shiftKey
        const key = e.key
        const isUndo = isCtrl && key.toLowerCase() === "z" && !isShift
        const isRedo = isCtrl && key.toLowerCase() === "z" && isShift
        const isSave = isCtrl && key.toLowerCase() === "s"
        const isCopy = isCtrl && key.toLowerCase() === "c"
        const isPaste = isCtrl && key.toLowerCase() === "v"
        const isDelete = key === 'Delete'
        const isBackspace = key === 'Backspace'
        const isHoldingCtrl = isCtrl

        if (isUndo && !debug) {
          e.preventDefault()
          if (!canUndo || _.isEqual(past[past.length - 1], firstElements)) {
            reset()
            setNodes([...firstElements.nodes])
            setEdges([...firstElements.edges])
            setElements({...firstElements})
          } else {
            undo()
            const lastPast = past?.[past.length - 1]
            if (!lastPast || lastPast.nodes.length === 0 || lastPast.edges.length === 0) {
              reset()
              setElements({...firstElements})
            } else {
              setNodes([...lastPast.nodes])
              setEdges([...lastPast.edges])
            }
          }
        }

        if (isRedo && !debug) {
          e.preventDefault()
          if (!canRedo || _.isEqual(elements, firstElements)) {
            setNodes([...elements.nodes])
            setEdges([...elements.edges])
          } else {
            redo()
            const firstFuture = future?.[0]
            if (!firstFuture || firstFuture.nodes.length === 0 || firstFuture.edges.length === 0) {
              reset()
              setElements({...firstElements})
            } else {
              setNodes([...firstFuture.nodes])
              setEdges([...firstFuture.edges])
            }
          }
        }

        if (isSave && !debug) {
          e.preventDefault()
          await handleSave(null, true, true)
          if (timeoutAutoSaveRef.current) {
            clearTimeout(timeoutAutoSaveRef.current)
          }
        }

        if (isHoldingCtrl) {
          setPanningMouseButton([0, 1]);
        }

        if (isCopy && !debug) {
          handleCopy(e)
        }

        if (isPaste && !debug) {
          handlePaste(e)
        }

        if ((isDelete || isBackspace) && !debug) {
          // e.preventDefault()
          handleDelete()
        }
      }

      const handleKeyUp = async (e: KeyboardEvent) => {
        const isCtrl = isCtrlKeyPressed(e)

        if (!isCtrl) {
          setPanningMouseButton([1]);
        }
      }

      window.addEventListener("keydown", handleKeyDown)
      window.addEventListener("keyup", handleKeyUp)
      return () => {
        window.removeEventListener("keydown", handleKeyDown)
        window.removeEventListener("keyup", handleKeyUp)
      }
    },
    [isDirty, elements, debug]
  )

  useEffect(
    () => {
      if (flowInstance && elements) {
        if (!_.isEqual({nodes: flowInstance.getNodes(), edges: flowInstance.getEdges()}, elements)) {
          setElements({
            nodes: flowInstance.getNodes(),
            edges: flowInstance.getEdges()
          })
        }
      }
    },
    [isDirty]
  )

  // useEffect(
  //   () => {
  //     const intervalSaveFlow = async () => {
  //       if (flowInstance && flow && status && status === STUDIO_STATUS.DEV) {
  //         const storageFlowData = localStorage.getItem(LocalStorageKey.flowDevData(flow.id))
  //         if (storageFlowData) {
  //           await handleSave(storageFlowData)
  //         } else {
  //           const flow_data = JSON.stringify({
  //             nodes: [...flowInstance.getNodes()].map(node => ({
  //               ...node,
  //               selected: false,
  //               data: {...node.data, selected: false},
  //               style: {...node.style, opacity: 1}
  //             })),
  //             edges: [...flowInstance.getEdges()].map(edge => {
  //               if (edge.data?.isGoToBlock) {
  //                 return {
  //                   ...edge,
  //                   animated: false,
  //                   style: {
  //                     ...edge.style,
  //                     opacity: 0
  //                   }
  //                 }
  //               } else {
  //                 return {
  //                   ...edge,
  //                   animated: false,
  //                   style: {
  //                     ...edge.style,
  //                     stroke: 'white',
  //                     strokeWidth: 1,
  //                     strokeOpacity: 1,
  //                     opacity: 1
  //                   }
  //                 }
  //               }
  //             }),
  //           })
  //           await handleSave(flow_data)
  //         }
  //       }
  //     }
  //
  //     if (flowInstance && flow && status && isDirty > 0) {
  //       if (intervalAutoSaveRef.current) {
  //         clearInterval(intervalAutoSaveRef.current)
  //       }
  //       intervalAutoSaveRef.current = setInterval(intervalSaveFlow, AUTO_SAVE_INTERVAL)
  //     }
  //
  //     return () => {
  //       if (intervalAutoSaveRef.current) {
  //         clearInterval(intervalAutoSaveRef.current)
  //       }
  //     }
  //   },
  //   [flowInstance, flow, status, isDirty]
  // )

  useEffect(
    () => {
      const autoSaveFlowToStorage = async () => {
        if (flowInstance && status === STUDIO_STATUS.DEV) {
          const flow_data = JSON.stringify({
            nodes: [...flowInstance.getNodes()].map(node => ({
              ...node,
              selected: false,
              data: {...node.data, selected: false},
              style: {...node.style, opacity: 1}
            })),
            edges: [...flowInstance.getEdges()].map(edge => {
              if (edge.data?.isGoToBlock) {
                return {
                  ...edge,
                  animated: false,
                  style: {
                    ...edge.style,
                    opacity: 0
                  }
                }
              } else {
                return {
                  ...edge,
                  animated: false,
                  style: {
                    ...edge.style,
                    stroke: 'white',
                    strokeWidth: 1,
                    strokeOpacity: 1,
                    opacity: 1
                  }
                }
              }
            }),
          })
          localStorage.setItem(LocalStorageKey.flowDevData(flow.id), flow_data)
          localStorage.setItem(LocalStorageKey.flowDevDataUpdatedAt(flow.id), new Date().toISOString())
        }
      }

      if (status === STUDIO_STATUS.DEV) {
        timeoutAutoSaveRef.current = setTimeout(autoSaveFlowToStorage, SAVE_DEBOUNCE)
      }

      return () => {
        if (timeoutAutoSaveRef.current) {
          clearTimeout(timeoutAutoSaveRef.current)
        }
      }
    },
    [flowInstance, flow, status]
  )

  useEffect(
    () => {
      if (debug) {
        setNodes((nds) =>
          nds.map((node) => {
            node.data = {
              ...node.data,
              debug: true,
            }
            node.style = {
              ...node.style,
              opacity: 0.3
            }
            return node
          })
        )
        setEdges((eds) =>
          eds.map((edge) => {
            edge.animated = false
            edge.style = {
              ...edge.style,
              opacity: edge.data?.isGoToBlock ? 0 : 0.3
            }
            return edge
          })
        )
      } else {
        setNodes((nds) =>
          nds.map((node) => {
            node.data = {
              ...node.data,
              selected: false,
              debug: false
            }
            node.style = {
              ...node.style,
              opacity: 1
            }
            return node
          })
        )
        setEdges((eds) =>
          eds.map((edge) => {
            edge.animated = false
            edge.style = {
              ...edge.style,
              stroke: 'white',
              strokeWidth: 1,
              strokeOpacity: 1,
              opacity: edge.data?.isGoToBlock ? 0 : 1
            }
            return edge
          })
        )
      }
    },
    [debug, setNodes, setEdges]
  )

  useEffect(
    () => {
      if (socket && conversationId) {
        let node_prompt_id = null
        let isPrompt = false
        socket.on('message_debug', (data) => {
          const conversation_id = data.conversation_id
          const node_id = data.node_id
          const edgeFocus = data.edge
          isPrompt = data.is_prompt
          if (conversationId === conversation_id) {
            if (node_prompt_id && node_prompt_id.includes('question')) {
              setNodes((nds) =>
                nds.map((node) => {
                  node.data = {
                    ...node.data,
                    selected: false,
                    debug: false
                  }
                  node.style = {
                    ...node.style,
                    opacity: 0.3
                  }
                  return node
                })
              )
              setEdges((eds) =>
                eds.map((edge) => {
                  edge.animated = false
                  edge.style = {
                    ...edge.style,
                    stroke: 'white',
                    strokeWidth: 1,
                    strokeOpacity: 1,
                    opacity: edge.data?.isGoToBlock ? 0 : 0.3
                  }
                  return edge
                })
              )
              isPrompt = false
              node_prompt_id = null
            }

            if (node_id.includes('start')) {
              setNodes((nds) =>
                nds.map((node) => {
                  node.data = {
                    ...node.data,
                    selected: false,
                    debug: false
                  }
                  node.style = {
                    ...node.style,
                    opacity: 0.3
                  }
                  return node
                })
              )
              setEdges((eds) =>
                eds.map((edge) => {
                  edge.animated = false
                  edge.style = {
                    ...edge.style,
                    stroke: 'white',
                    strokeWidth: 1,
                    strokeOpacity: 1,
                    opacity: edge.data?.isGoToBlock ? 0 : 0.3
                  }
                  return edge
                })
              )
            }

            setNodes((nds) =>
              nds.map((node) => {
                if (node.id === node_id) {
                  node.data = {
                    ...node.data,
                    selected: true,
                    debug: true
                  }
                  node.style = {
                    ...node.style,
                    opacity: 1
                  }
                }
                return node
              })
            )
            setEdges((eds) =>
              eds.map((edge) => {
                if (edgeFocus && edge.sourceHandle === edgeFocus.sourceHandle && edge.targetHandle === edgeFocus.targetHandle) {
                  edge.animated = true
                  edge.style = {
                    ...edge.style,
                    stroke: 'yellow',
                    strokeWidth: 2,
                    strokeOpacity: 1,
                    opacity: 1
                  }
                }
                return edge
              })
            )

            if (isPrompt) {
              node_prompt_id = node_id
            }
          }
        })
      }
    },
    [socket, conversationId]
  )

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      localStorage.removeItem(LocalStorageKey.flowDevData(flowId))
      localStorage.removeItem(LocalStorageKey.flowDevDataUpdatedAt(flowId))
      localStorage.removeItem(LocalStorageKey.variableLocation())
      localStorage.removeItem(LocalStorageKey.mousePositionFlow(flowId))
      for (const key in localStorage) {
        if (key.includes('nodePositionPasted') && key.includes(String(flowId))) {
          localStorage.removeItem(key)
        }
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  useEffect(
    () => {
      return () => {
        setFlowInstance(null)
        setFlow(null)
        setNodes([])
        setEdges([])
        setFirstElements({nodes: [], edges: []})
        setElements({nodes: [], edges: []})
        localStorage.removeItem(LocalStorageKey.flowDevData(flowId))
        localStorage.removeItem(LocalStorageKey.flowDevDataUpdatedAt(flowId))
        localStorage.removeItem(LocalStorageKey.variableLocation())
        localStorage.removeItem(LocalStorageKey.mousePositionFlow(flowId))
        for (const key in localStorage) {
          if (key.includes('nodePositionPasted') && key.includes(String(flowId))) {
            localStorage.removeItem(key)
          }
        }
        setMenu(null)
        menuOpenRef.current = false
        setDebug(false)
      };
    },
    [flowId]
  )

  useEffect(() => {
    handleSave(null, false, true);
  }, [triggerSave]);

  return (
    <ReactFlow
      fitView
      minZoom={MIN_ZOOM}
      maxZoom={MAX_ZOOM}
      nodes={nodes}
      edges={edges}
      nodeTypes={nodeTypes}
      edgeTypes={edgeTypes}
      onInit={setFlowInstance}
      onNodeClick={handleNodeClick}
      onNodeDragStart={handleNodeDragStart}
      onNodeDragStop={handleNodeDragStop}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={handleConnect}
      onConnectStart={handleConnectStart}
      onConnectEnd={handleConnectEnd}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onPaneClick={handlePanelClick}
      onPaneContextMenu={handleConnectEnd}
      multiSelectionKeyCode={dialogShowing ? null : ["Control", "Meta"]}
      onKeyDown={handleKeyDown}
      panOnDrag={panningMouseButton}
      selectionOnDrag={true}
      selectNodesOnDrag={true}
      selectionMode={SelectionMode.Partial}
      onSelectionChange={handleSelectionChange}
      onSelectionDragStop={handleSelectionDragStop}
      edgesUpdatable={status && status === STUDIO_STATUS.DEV && !menu}
      nodesDraggable={!debug && status && status === STUDIO_STATUS.DEV && !menu}
      nodesConnectable={status && status === STUDIO_STATUS.DEV && !menu}
      deleteKeyCode={null}
      zoomOnScroll={!menu}
    >
      <Background
        color="#94a2b8"
        gap={16}
        style={{opacity: (debug || selecting) ? 0.3 : 1}}
      />
      <MiniMapToolBar/>
      {
        menu &&
        <NodeContextMenu
          onClick={handlePanelClick}
          {...{...menu, paramSourceNode, listNodeCollapsed}}
        />
      }
    </ReactFlow>
  )
})

export default FlowCanvas;
