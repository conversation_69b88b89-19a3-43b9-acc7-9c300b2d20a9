import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { MENU_ITEMS } from '@core/constants';
import { IAccount } from '@core/models';
import { UserAiStore } from '@core/stores';
import { DxSnackBar } from '@dx-ui/ui';
import { environment } from '@env/environment';
import { AuthUtils, CommonUtils } from '@shared/utils';
import { Observable, of } from 'rxjs';
import { catchError, last, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private http = inject(HttpClient);
  private userAiStore = inject(UserAiStore);
  private snackBar = inject(DxSnackBar);

  get accessToken(): string | null {
    return (
      localStorage.getItem('accessToken') ??
      sessionStorage.getItem('accessToken')
    );
  }

  login(username: string, password: string): Observable<any> {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);
    formData.append('grant_type', 'password');
    return this.http.post<any>(
      `${environment.SERVER_URL}/auth/token`,
      formData
    );
  }

  signUp(body: any): Observable<any> {
    return this.http.post<any>(`${environment.SERVER_URL}/auth/register`, body);
  }

  logout(): void {
    this.userAiStore.setCurrentUser(null);
    this.userAiStore.setCurrentAi(null);
    const dx_connect_tour_guides = localStorage.getItem('dx-connect-completed-steps');
    const skip_all_tours = localStorage.getItem('dx-connect-skip-all-tours');
    localStorage.clear();
    if (dx_connect_tour_guides) {
      localStorage.setItem('dx-connect-completed-steps', dx_connect_tour_guides);
    }
    if (skip_all_tours) {
      localStorage.setItem('dx-connect-skip-all-tours', skip_all_tours);
    }
    sessionStorage.clear();
  }

  getAccount() {
    return this.http
      .get<IAccount>(`${environment.SERVER_URL}/users/account`)
      .pipe(
        catchError((error) => {
          this.snackBar.open(
            `Error fetching user profile: ${error.message}`,
            '',
            {
              panelClass: 'dx-snack-bar-error',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            }
          );
          return of(null);
        })
      );
  }

  activateAccount(activationId: string) {
    return this.http.get(
      `${environment.SERVER_URL}/auth/activate/${activationId}`
    );
  }

  changePassword(body: any): Observable<any> {
    return this.http.put(
      `${environment.SERVER_URL}/users/change-password`,
      body
    );
  }

  forgotPassword(body: any): Observable<any> {
    return this.http.post(
      `${environment.SERVER_URL}/users/forget-password`,
      body
    );
  }

  resetPassword(token: string, body: any): Observable<any> {
    return this.http.put(
      `${environment.SERVER_URL}/users/reset-password/${token}`,
      body
    );
  }

  check(): Observable<boolean> {
    const token = this.accessToken;
    if (!token || AuthUtils.isTokenExpired(token)) {
      return of(false);
    }

    if (this.userAiStore.currentUser() && this.userAiStore.currentAi()) {
      return of(true);
    }

    return this.getAccount().pipe(
      last(),
      switchMap((user: IAccount | null) => {
        if (!user) return of(false);
        this.userAiStore.setCurrentUser(user);
        return of(true);
      }),
      catchError(() => of(false))
    );
  }

  checkRole(
    url: string,
    dataRoles: string[] | null,
    dataRolesAI: string[] | null
  ): Observable<boolean> {
    const token = this.accessToken;
    if (!token || AuthUtils.isTokenExpired(token)) {
      return of(false);
    }

    const user = this.userAiStore.currentUser();
    const ai = this.userAiStore.currentAi();

    const normalizedUrl = CommonUtils.normalizeUrl(url);
    const rolesByLink = this.findRolesByLink(normalizedUrl);
    const rolesAIByLink = this.findRolesAiByLink(normalizedUrl);

    const hasUserRole =
      user?.role &&
      (rolesByLink?.includes(user.role) || dataRoles?.includes(user.role));
    const hasAiRole =
      ai?.role &&
      (rolesAIByLink?.includes(ai.role) || dataRolesAI?.includes(ai.role));

    return of(
      Boolean(
        ((rolesByLink && rolesAIByLink) || (dataRoles && dataRolesAI)) &&
          hasUserRole &&
          hasAiRole
      )
    );
  }

  private findRolesByLink(link: string): string[] | null {
    return this.findRolesRecursive(link, MENU_ITEMS, 'roles');
  }

  private findRolesAiByLink(link: string): string[] | null {
    return this.findRolesRecursive(link, MENU_ITEMS, 'rolesAI');
  }

  private findRolesRecursive(
    link: string,
    items: any[],
    key: 'roles' | 'rolesAI'
  ): string[] | null {
    for (const item of items) {
      if (item.link === link) {
        return item[key] ?? null;
      }
      if (item.children) {
        const found = this.findRolesRecursive(link, item.children, key);
        if (found) return found;
      }
    }
    return null;
  }
}
