{"name": "dx-connect-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration development", "build:labs": "ng build --configuration labs", "build:prod": "ng build --configuration production", "build:flow-editor": "tsc -p tsconfig.flow-editor.json", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.8", "@angular/cdk": "^19.2.18", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.15", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@ant-design/icons": "^5.4.0", "@dx-ui/ui": "^1.1.18", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^3.6.0", "@ng-icons/core": "^31.3.0", "@ng-icons/font-awesome": "^31.3.0", "@ng-icons/heroicons": "^31.3.0", "@ngrx/signals": "^19.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@popperjs/core": "^2.11.8", "@remixicon/react": "^4.2.0", "@tailwindcss/postcss": "^4.1.8", "angular-split": "^19.0.0", "antd": "^5.17.3", "autoprefixer": "^10.4.21", "axios": "^1.7.2", "highcharts": "^12.2.0", "highcharts-angular": "^4.0.1", "highlight.js": "^11.11.1", "moment": "^2.30.1", "monaco-editor": "^0.52.2", "ngx-clipboard": "^16.0.0", "ngx-infinite-scroll": "^19.0.0", "ngx-joyride": "^2.5.0", "ngx-monaco-editor-v2": "^19.0.2", "ngx-scrollbar": "^18.0.0", "postcss": "^8.5.4", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-hook-form": "^7.43.7", "reactflow": "^11.11.4", "rxjs": "~7.8.0", "socket.io-client": "^4.8.1", "styled-components": "^6.1.11", "tailwindcss": "^4.1.8", "tslib": "^2.3.0", "use-undoable": "^5.0.0", "uuid": "^11.1.0", "vite-plugin-monaco-editor": "^1.1.0", "yup": "^1.0.2", "zone.js": "~0.15.0", "zustand": "^4.5.2"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.0", "@angular/localize": "^19.2.8", "@types/highlight.js": "^9.12.4", "@types/jasmine": "~5.1.0", "@types/lodash": "^4.17.16", "@types/react": "^18.3.2", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.30.1", "typescript": "~5.8.3"}}